{"version": "5", "dialect": "mysql", "id": "faa8111b-ebc6-43eb-8ce5-050f113965a4", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"api_keys": {"name": "api_keys", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "key_hash": {"name": "key_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "usage_count": {"name": "usage_count", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"api_keys_user_id_users_id_fk": {"name": "api_keys_user_id_users_id_fk", "tableFrom": "api_keys", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"api_keys_id": {"name": "api_keys_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "checkout_pages": {"name": "checkout_pages", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_name": {"name": "product_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price": {"name": "price", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('USD')"}, "smtp_config_id": {"name": "smtp_config_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "customer_fields": {"name": "customer_fields", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "theme": {"name": "theme", "type": "json", "primaryKey": false, "notNull": true, "autoincrement": false}, "payment_gateways": {"name": "payment_gateways", "type": "json", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"checkout_pages_user_id_users_id_fk": {"name": "checkout_pages_user_id_users_id_fk", "tableFrom": "checkout_pages", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "checkout_pages_smtp_config_id_smtp_configs_id_fk": {"name": "checkout_pages_smtp_config_id_smtp_configs_id_fk", "tableFrom": "checkout_pages", "tableTo": "smtp_configs", "columnsFrom": ["smtp_config_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"checkout_pages_id": {"name": "checkout_pages_id", "columns": ["id"]}}, "uniqueConstraints": {"checkout_pages_slug_unique": {"name": "checkout_pages_slug_unique", "columns": ["slug"]}}, "checkConstraint": {}}, "email_templates": {"name": "email_templates", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "html_content": {"name": "html_content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "text_content": {"name": "text_content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "variables": {"name": "variables", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"email_templates_user_id_users_id_fk": {"name": "email_templates_user_id_users_id_fk", "tableFrom": "email_templates", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"email_templates_id": {"name": "email_templates_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "login_history": {"name": "login_history", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false}, "failure_reason": {"name": "failure_reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"login_history_user_id_users_id_fk": {"name": "login_history_user_id_users_id_fk", "tableFrom": "login_history", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"login_history_id": {"name": "login_history_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "payments": {"name": "payments", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "checkout_page_id": {"name": "checkout_page_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "amount": {"name": "amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "gateway": {"name": "gateway", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "gateway_transaction_id": {"name": "gateway_transaction_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('pending')"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"payments_user_id_users_id_fk": {"name": "payments_user_id_users_id_fk", "tableFrom": "payments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payments_checkout_page_id_checkout_pages_id_fk": {"name": "payments_checkout_page_id_checkout_pages_id_fk", "tableFrom": "payments", "tableTo": "checkout_pages", "columnsFrom": ["checkout_page_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"payments_id": {"name": "payments_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "pdf_operations": {"name": "pdf_operations", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "operation": {"name": "operation", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_size": {"name": "file_size", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('processing')"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"pdf_operations_user_id_users_id_fk": {"name": "pdf_operations_user_id_users_id_fk", "tableFrom": "pdf_operations", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"pdf_operations_id": {"name": "pdf_operations_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "rate_limits": {"name": "rate_limits", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "endpoint": {"name": "endpoint", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "requests": {"name": "requests", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "window_start": {"name": "window_start", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"rate_limits_id": {"name": "rate_limits_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "site_config": {"name": "site_config", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "json", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"site_config_id": {"name": "site_config_id", "columns": ["id"]}}, "uniqueConstraints": {"site_config_key_unique": {"name": "site_config_key_unique", "columns": ["key"]}}, "checkConstraint": {}}, "smtp_configs": {"name": "smtp_configs", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "host": {"name": "host", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "port": {"name": "port", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "secure": {"name": "secure", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "from_email": {"name": "from_email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "from_name": {"name": "from_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "routing_rules": {"name": "routing_rules", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"smtp_configs_user_id_users_id_fk": {"name": "smtp_configs_user_id_users_id_fk", "tableFrom": "smtp_configs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"smtp_configs_id": {"name": "smtp_configs_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "system_settings": {"name": "system_settings", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "json", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"system_settings_id": {"name": "system_settings_id", "columns": ["id"]}}, "uniqueConstraints": {"system_settings_key_unique": {"name": "system_settings_key_unique", "columns": ["key"]}}, "checkConstraint": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('user')"}, "is_premium": {"name": "is_premium", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "premium_expires_at": {"name": "premium_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "usage_limit": {"name": "usage_limit", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 5}, "usage_count": {"name": "usage_count", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "api_key": {"name": "api_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "custom_theme": {"name": "custom_theme", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "email_verification_token": {"name": "email_verification_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password_reset_token": {"name": "password_reset_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password_reset_expires": {"name": "password_reset_expires", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "two_factor_secret": {"name": "two_factor_secret", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "two_factor_enabled": {"name": "two_factor_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "login_attempts": {"name": "login_attempts", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "locked_until": {"name": "locked_until", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"users_id": {"name": "users_id", "columns": ["id"]}}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "columns": ["email"]}}, "checkConstraint": {}}, "webhooks": {"name": "webhooks", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "events": {"name": "events", "type": "json", "primaryKey": false, "notNull": true, "autoincrement": false}, "secret": {"name": "secret", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "last_triggered": {"name": "last_triggered", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"webhooks_user_id_users_id_fk": {"name": "webhooks_user_id_users_id_fk", "tableFrom": "webhooks", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"webhooks_id": {"name": "webhooks_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}