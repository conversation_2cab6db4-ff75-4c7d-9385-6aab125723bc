import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON>, 
  <PERSON>ie,
  Settings,
  Eye,
  BarChart,
  Share2,
  Lock,
  Globe,
  CheckCircle,
  AlertTriangle
} from "lucide-react";

export default function CookiePolicy() {
  const cookieTypes = [
    {
      type: "Essential Cookies",
      description: "Necessary for the website to function properly",
      icon: Lock,
      color: "text-red-500",
      examples: ["Authentication", "Security", "Session Management"]
    },
    {
      type: "Performance Cookies", 
      description: "Help us understand how visitors use our website",
      icon: <PERSON><PERSON><PERSON>,
      color: "text-blue-500",
      examples: ["Analytics", "Performance Monitoring", "A/B Testing"]
    },
    {
      type: "Functionality Cookies",
      description: "Enhance your experience on our website", 
      icon: Settings,
      color: "text-green-500",
      examples: ["Preferences", "Theme", "Tool History"]
    },
    {
      type: "Marketing Cookies",
      description: "Help us show you relevant advertisements",
      icon: Share2,
      color: "text-purple-500", 
      examples: ["Advertising", "Social Media", "Remarketing"]
    }
  ];

  const cookieDetails = [
    { name: "session_id", purpose: "User authentication", duration: "Session", type: "Essential" },
    { name: "csrf_token", purpose: "Security protection", duration: "Session", type: "Essential" },
    { name: "user_prefs", purpose: "User preferences", duration: "1 year", type: "Functionality" },
    { name: "_ga", purpose: "Google Analytics", duration: "2 years", type: "Performance" },
    { name: "_gid", purpose: "Google Analytics", duration: "24 hours", type: "Performance" },
    { name: "theme", purpose: "UI theme preference", duration: "1 year", type: "Functionality" }
  ];

  const thirdPartyServices = [
    {
      name: "Google Analytics",
      purpose: "Website analytics and performance monitoring",
      data: "Page views, user interactions, demographics",
      optOut: "Google Analytics Opt-out"
    },
    {
      name: "Stripe", 
      purpose: "Payment processing and fraud prevention",
      data: "Payment information, transaction data",
      optOut: "Stripe Privacy Policy"
    },
    {
      name: "PayPal",
      purpose: "Payment processing",
      data: "Payment and transaction information", 
      optOut: "PayPal Privacy Policy"
    }
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Essential': return 'bg-red-500';
      case 'Performance': return 'bg-blue-500';
      case 'Functionality': return 'bg-green-500';
      case 'Marketing': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Navigation */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/">
                <span className="text-2xl font-bold text-primary">PDFTools Pro</span>
              </Link>
              <div className="hidden md:block ml-10">
                <div className="flex items-baseline space-x-4">
                  <Link href="/pricing">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Pricing</span>
                  </Link>
                  <Link href="/about">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">About</span>
                  </Link>
                  <Link href="/contact">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Contact</span>
                  </Link>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-4 flex items-center md:ml-6 space-x-4">
                <Link href="/auth">
                  <Button variant="ghost">Login</Button>
                </Link>
                <Link href="/auth">
                  <Button>Sign Up Free</Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/5 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-700 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-primary/10 rounded-full">
              <Cookie className="h-12 w-12 text-primary" />
            </div>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Cookie Policy
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            Learn about how PDFTools Pro uses cookies, what types we use, and how to manage your cookie preferences.
          </p>
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 max-w-2xl mx-auto">
            <div className="flex items-center justify-center mb-2">
              <Eye className="h-6 w-6 text-blue-600 mr-2" />
              <span className="text-lg font-semibold text-blue-800 dark:text-blue-200">Transparency First</span>
            </div>
            <p className="text-blue-700 dark:text-blue-300">
              We believe in being transparent about how we use cookies to improve your experience.
            </p>
          </div>
        </div>
      </section>

      {/* What Are Cookies */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              What Are Cookies?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Small text files that help us provide you with a better experience
            </p>
          </div>

          <Card className="max-w-4xl mx-auto">
            <CardContent className="p-8">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                  <Cookie className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Cookies Explained
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Cookies are small text files that are stored on your device when you visit our website. 
                    They help us provide you with a better experience by remembering your preferences and improving our services.
                  </p>
                  <p className="text-gray-600 dark:text-gray-300">
                    We use cookies responsibly and in compliance with data protection laws including GDPR and CCPA. 
                    You have full control over which cookies you accept.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Types of Cookies */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Types of Cookies We Use
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Different categories of cookies serve different purposes
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {cookieTypes.map((cookie, index) => {
              const Icon = cookie.icon;
              return (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <Icon className={`h-5 w-5 ${cookie.color}`} />
                      </div>
                      <CardTitle className="text-lg">{cookie.type}</CardTitle>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300">{cookie.description}</p>
                  </CardHeader>
                  <CardContent>
                    <div>
                      <h4 className="font-semibold mb-2">Examples:</h4>
                      <ul className="space-y-1">
                        {cookie.examples.map((example, exampleIndex) => (
                          <li key={exampleIndex} className="flex items-center">
                            <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                            <span className="text-sm text-gray-600 dark:text-gray-300">{example}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Cookie Details Table */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Cookie Details
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Specific information about the cookies we use
            </p>
          </div>

          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Cookie Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Purpose
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Duration
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Type
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {cookieDetails.map((cookie, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <code className="text-sm font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                            {cookie.name}
                          </code>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-300">
                          {cookie.purpose}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-300">
                          {cookie.duration}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Badge className={getTypeColor(cookie.type)}>
                            {cookie.type}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Third Party Services */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Third-Party Services
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              External services that may set cookies on our website
            </p>
          </div>

          <div className="space-y-6">
            {thirdPartyServices.map((service, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center">
                      <Globe className="h-5 w-5 mr-2" />
                      {service.name}
                    </CardTitle>
                    <Button variant="outline" size="sm">
                      {service.optOut}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-2">Purpose</h4>
                      <p className="text-gray-600 dark:text-gray-300">{service.purpose}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Data Collected</h4>
                      <p className="text-gray-600 dark:text-gray-300">{service.data}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Managing Cookies */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Managing Your Cookies
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              You have full control over your cookie preferences
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  Browser Settings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Chrome</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Settings → Privacy and Security → Cookies
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Firefox</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Settings → Privacy & Security
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Safari</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Preferences → Privacy
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Edge</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Settings → Cookies and site permissions
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2" />
                  Our Cookie Preferences
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-600 dark:text-gray-300">
                    You can also manage cookies through our preference center:
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      <span className="text-sm">Visit your account settings</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      <span className="text-sm">Go to "Privacy Preferences"</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      <span className="text-sm">Toggle cookie categories on/off</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      <span className="text-sm">Save your preferences</span>
                    </li>
                  </ul>
                  <Button className="w-full mt-4">
                    Manage Cookie Preferences
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Your Rights */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Your Rights
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Under data protection laws, you have these rights regarding cookies
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { title: "Access", description: "Request information about cookies we use", icon: Eye },
              { title: "Rectification", description: "Correct inaccurate cookie data", icon: Settings },
              { title: "Erasure", description: "Request deletion of cookie data", icon: AlertTriangle },
              { title: "Portability", description: "Receive your cookie data in a portable format", icon: Share2 }
            ].map((right, index) => {
              const Icon = right.icon;
              return (
                <Card key={index} className="text-center">
                  <CardHeader>
                    <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                      <Icon className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-lg">{right.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-300">{right.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Contact & Updates */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Contact Us</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  If you have questions about our cookie policy:
                </p>
                <div className="space-y-2">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Address:</strong> PDFTools Pro, 123 Tech Street, San Francisco, CA 94105</p>
                  <p><strong>Phone:</strong> ******-PDF-TOOL</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Policy Updates</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  We may update this cookie policy to reflect changes in:
                </p>
                <ul className="space-y-1">
                  <li>• Our practices</li>
                  <li>• Legal requirements</li>
                  <li>• Technology updates</li>
                  <li>• Service improvements</li>
                </ul>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
                  <strong>Last updated:</strong> {new Date().toLocaleDateString()}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-foreground text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-1">
              <div className="flex items-center mb-4">
                <span className="text-2xl font-bold">PDFTools Pro</span>
              </div>
              <p className="text-gray-300 mb-4">
                Professional PDF toolkit with custom checkout page generation for businesses and creators.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/tools"><span className="hover:text-white cursor-pointer">PDF Tools</span></Link></li>
                <li><Link href="/documentation"><span className="hover:text-white cursor-pointer">Documentation</span></Link></li>
                <li><Link href="/api-reference"><span className="hover:text-white cursor-pointer">API Reference</span></Link></li>
                <li><Link href="/status"><span className="hover:text-white cursor-pointer">Status Page</span></Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/documentation"><span className="hover:text-white cursor-pointer">Documentation</span></Link></li>
                <li><Link href="/api-reference"><span className="hover:text-white cursor-pointer">API Reference</span></Link></li>
                <li><Link href="/contact"><span className="hover:text-white cursor-pointer">Contact Support</span></Link></li>
                <li><Link href="/status"><span className="hover:text-white cursor-pointer">Status Page</span></Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/privacy"><span className="hover:text-white cursor-pointer">Privacy Policy</span></Link></li>
                <li><Link href="/terms"><span className="hover:text-white cursor-pointer">Terms of Service</span></Link></li>
                <li><Link href="/refund-policy"><span className="hover:text-white cursor-pointer">Refund Policy</span></Link></li>
                <li><Link href="/cookie-policy"><span className="hover:text-white cursor-pointer">Cookie Policy</span></Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-300 text-sm">
              © 2024 PDFTools Pro. All rights reserved.
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <span className="text-gray-300 text-sm">Secured by</span>
              <div className="flex items-center space-x-2">
                <Shield className="text-green-500 h-4 w-4" />
                <span className="text-sm">256-bit SSL</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
