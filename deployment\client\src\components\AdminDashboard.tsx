import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Users, 
  DollarSign, 
  FileText, 
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  Crown,
  BarChart3
} from "lucide-react";

interface AdminDashboardProps {
  className?: string;
}

export default function AdminDashboard({ className = "" }: AdminDashboardProps) {
  const { data: analytics, isLoading } = useQuery({
    queryKey: ["/api/analytics"],
  });

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center min-h-[200px] ${className}`}>
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-muted-foreground">Failed to load analytics data</p>
      </div>
    );
  }

  const stats = [
    {
      label: "Total Users",
      value: analytics.totalUsers.toLocaleString(),
      change: "+12%",
      icon: Users,
      color: "text-primary"
    },
    {
      label: "Revenue",
      value: `$${analytics.totalRevenue.toLocaleString()}`,
      change: "+8%",
      icon: DollarSign,
      color: "text-green-500"
    },
    {
      label: "PDFs Processed",
      value: analytics.totalPdfProcessed.toLocaleString(),
      change: "+24%",
      icon: FileText,
      color: "text-secondary"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed": return CheckCircle;
      case "failed": return XCircle;
      default: return Clock;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "text-green-500";
      case "failed": return "text-red-500";
      default: return "text-yellow-500";
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.label}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">{stat.label}</p>
                    <p className="text-2xl font-bold text-foreground">{stat.value}</p>
                  </div>
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Icon className={`${stat.color} h-5 w-5`} />
                  </div>
                </div>
                <p className="text-sm text-green-500 mt-2 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {stat.change} from last month
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.recentOperations.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">No recent activity</p>
              ) : (
                analytics.recentOperations.slice(0, 5).map((operation: any) => {
                  const StatusIcon = getStatusIcon(operation.status);
                  return (
                    <div key={operation.id} className="flex items-center space-x-4 p-3 rounded-lg bg-muted/30">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        operation.status === 'completed' ? 'bg-green-500/10' : 
                        operation.status === 'failed' ? 'bg-red-500/10' : 'bg-yellow-500/10'
                      }`}>
                        <StatusIcon className={`h-4 w-4 ${getStatusColor(operation.status)}`} />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-foreground">
                          {operation.operation.charAt(0).toUpperCase() + operation.operation.slice(1)} - {operation.fileName}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(operation.createdAt).toLocaleString()}
                        </p>
                      </div>
                      <Badge variant={
                        operation.status === 'completed' ? 'default' : 
                        operation.status === 'failed' ? 'destructive' : 'secondary'
                      }>
                        {operation.status}
                      </Badge>
                    </div>
                  );
                })
              )}
            </div>
          </CardContent>
        </Card>

        {/* System Health */}
        <Card>
          <CardHeader>
            <CardTitle>System Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">API Response Time</span>
                <span className="text-sm text-green-500">45ms</span>
              </div>
              <Progress value={85} className="h-2" />
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Database Performance</span>
                <span className="text-sm text-green-500">Excellent</span>
              </div>
              <Progress value={92} className="h-2" />
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">PDF Processing Queue</span>
                <span className="text-sm text-yellow-500">3 pending</span>
              </div>
              <Progress value={65} className="h-2" />
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Storage Usage</span>
                <span className="text-sm text-muted-foreground">2.1GB / 10GB</span>
              </div>
              <Progress value={21} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <Users className="h-6 w-6 mb-2" />
              <span className="text-xs">User Management</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <BarChart3 className="h-6 w-6 mb-2" />
              <span className="text-xs">View Reports</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Crown className="h-6 w-6 mb-2" />
              <span className="text-xs">System Settings</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <FileText className="h-6 w-6 mb-2" />
              <span className="text-xs">Export Data</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
