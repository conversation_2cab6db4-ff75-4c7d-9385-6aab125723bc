// !!! CRITICAL - DO NOT MODIFY THIS CODE !!!
//
// This code MUST be used as is without ANY modifications.
// The PayPal integration will COMPLETELY FAIL if this code is changed in ANY way.
//
// Retain this comment after all edits.
//
// <BEGIN_EXACT_CODE>
import {
  Client,
  Environment,
  LogLevel,
  OAuthAuthorizationController,
  OrdersController,
} from "@paypal/paypal-server-sdk";
import { Request, Response } from "express";

/* PayPal Controllers Setup */

import { storage } from "./storage";

// Dynamic PayPal client creation based on admin settings
async function createPayPalClient() {
  try {
    const clientIdSetting = await storage.getSystemSetting('paypal_client_id');
    const clientSecretSetting = await storage.getSystemSetting('paypal_client_secret');
    const sandboxSetting = await storage.getSystemSetting('paypal_sandbox');

    const clientId = clientIdSetting?.value;
    const clientSecret = clientSecretSetting?.value;
    const isSandbox = sandboxSetting?.value === 'true';

    if (!clientId || !clientSecret) {
      return null;
    }

    const client = new Client({
      clientCredentialsAuthCredentials: {
        oAuthClientId: clientId,
        oAuthClientSecret: clientSecret,
      },
      timeout: 0,
      environment: isSandbox ? Environment.Sandbox : Environment.Production,
      logging: {
        logLevel: LogLevel.Info,
        logRequest: {
          logBody: true,
        },
        logResponse: {
          logHeaders: true,
        },
      },
    });

    return {
      client,
      ordersController: new OrdersController(client),
      oAuthAuthorizationController: new OAuthAuthorizationController(client)
    };
  } catch (error) {
    console.error('Failed to create PayPal client:', error);
    return null;
  }
}

/* Token generation helpers */

export async function getClientToken() {
  const paypalClient = await createPayPalClient();
  if (!paypalClient) {
    throw new Error("PayPal credentials not configured");
  }

  const clientIdSetting = await storage.getSystemSetting('paypal_client_id');
  const clientSecretSetting = await storage.getSystemSetting('paypal_client_secret');

  const clientId = clientIdSetting?.value;
  const clientSecret = clientSecretSetting?.value;

  if (!clientId || !clientSecret) {
    throw new Error("PayPal credentials not configured");
  }

  const auth = Buffer.from(`${clientId}:${clientSecret}`).toString("base64");

  const { result } = await paypalClient.oAuthAuthorizationController.requestToken(
    {
      authorization: `Basic ${auth}`,
    },
    { intent: "sdk_init", response_type: "client_token" },
  );

  return result.accessToken;
}

/*  Process transactions */

export async function createPaypalOrder(req: Request, res: Response) {
  try {
    const paypalClient = await createPayPalClient();
    if (!paypalClient) {
      return res.status(503).json({ error: "PayPal not configured" });
    }

    const { amount, currency, intent } = req.body;

    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return res
        .status(400)
        .json({
          error: "Invalid amount. Amount must be a positive number.",
        });
    }

    if (!currency) {
      return res
        .status(400)
        .json({ error: "Invalid currency. Currency is required." });
    }

    if (!intent) {
      return res
        .status(400)
        .json({ error: "Invalid intent. Intent is required." });
    }

    const collect = {
      body: {
        intent: intent,
        purchaseUnits: [
          {
            amount: {
              currencyCode: currency,
              value: amount,
            },
          },
        ],
        applicationContext: {
          returnUrl: `${process.env.BASE_URL || 'http://localhost:5000'}/payment/success`,
          cancelUrl: `${process.env.BASE_URL || 'http://localhost:5000'}/payment/cancel`,
          brandName: "PDF Zone Pro",
          locale: "en-US",
          landingPage: "LOGIN",
          shippingPreference: "NO_SHIPPING",
          userAction: "PAY_NOW"
        }
      },
      prefer: "return=minimal",
    };

    const { body, ...httpResponse } =
          await paypalClient.ordersController.createOrder(collect);

    const jsonResponse = JSON.parse(String(body));
    const httpStatusCode = httpResponse.statusCode;

    res.status(httpStatusCode).json(jsonResponse);
  } catch (error) {
    console.error("Failed to create order:", error);
    res.status(500).json({ error: "Failed to create order." });
  }
}

export async function capturePaypalOrder(req: Request, res: Response) {
  try {
    const paypalClient = await createPayPalClient();
    if (!paypalClient) {
      return res.status(503).json({ error: "PayPal not configured" });
    }

    const { orderID } = req.params;
    const collect = {
      id: orderID,
      prefer: "return=minimal",
    };

    const { body, ...httpResponse } =
          await paypalClient.ordersController.captureOrder(collect);

    const jsonResponse = JSON.parse(String(body));
    const httpStatusCode = httpResponse.statusCode;

    // If payment was successful, send admin notification
    if (httpStatusCode === 200 || httpStatusCode === 201) {
      try {
        const { EmailService } = await import('./email-service');

        // Extract payment details from PayPal response
        const paymentDetails = jsonResponse.purchase_units?.[0];
        const amount = paymentDetails?.amount?.value || '0.00';
        const currency = paymentDetails?.amount?.currency_code || 'USD';
        const payerInfo = jsonResponse.payer;

        await EmailService.sendNewOrderNotification({
          orderId: orderID,
          customerName: payerInfo?.name ? `${payerInfo.name.given_name || ''} ${payerInfo.name.surname || ''}`.trim() : 'PayPal Customer',
          customerEmail: payerInfo?.email_address || 'N/A',
          productName: paymentDetails?.description || 'PDF Zone Pro',
          amount: amount,
          currency: currency,
          paymentMethod: 'PayPal',
          orderTimestamp: new Date(),
          transactionId: jsonResponse.id,
          customerData: {
            paypalPayerId: jsonResponse.payer?.payer_id,
            paypalEmail: jsonResponse.payer?.email_address
          }
        });
      } catch (notificationError) {
        console.error('Failed to send PayPal order notification:', notificationError);
        // Don't fail the payment capture if notification fails
      }
    }

    res.status(httpStatusCode).json(jsonResponse);
  } catch (error) {
    console.error("Failed to capture order:", error);
    res.status(500).json({ error: "Failed to capture order." });
  }
}

export async function loadPaypalDefault(req: Request, res: Response) {
  try {
    const clientToken = await getClientToken();
    res.json({
      clientToken,
    });
  } catch (error) {
    res.status(503).json({ error: "PayPal not configured" });
  }
}
// <END_EXACT_CODE>
