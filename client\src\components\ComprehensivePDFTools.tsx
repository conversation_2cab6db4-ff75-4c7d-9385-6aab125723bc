import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/hooks/use-user";
import { Link } from "wouter";
import {
  FileText,
  RotateCw,
  Trash2,
  ArrowUpDown,
  Palette,
  Scissors,
  Wrench,
  Image,
  FileImage,
  Monitor,
  Smartphone,
  FileSpreadsheet,
  Presentation,
  Type,
  Download,
  Archive,
  Lock,
  Unlock,
  Crown,
  Upload
} from "lucide-react";

export default function ComprehensivePDFTools() {
  const { toast } = useToast();
  const { user } = useUser();
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedTool, setSelectedTool] = useState<string | null>(null);

  const isPremium = user?.isPremium || user?.role === 'admin' || user?.username === 'admin' || false;

  const processFile = async (endpoint: string, formData: FormData) => {
    setIsProcessing(true);
    try {
      const response = await fetch(endpoint, {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Processing failed");
      }

      // Handle different response types
      const contentType = response.headers.get("content-type");
      if (contentType?.includes("application/pdf")) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `processed-${Date.now()}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const result = await response.json();
        toast({
          title: "Success",
          description: result.message || "File processed successfully",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Processing failed",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const pdfTools = [
    // Row 1
    {
      id: 'merge',
      title: 'Merge PDF',
      description: 'Merge multiple PDF files into one',
      icon: FileText,
      endpoint: '/api/pdf/merge',
      isPremium: false,
      acceptMultiple: true,
      accept: '.pdf',
      color: 'bg-blue-50 border-blue-200 hover:bg-blue-100'
    },
    {
      id: 'rotate',
      title: 'Rotate PDF',
      description: 'Rotate PDF pages by 90, 180, or 270 degrees',
      icon: RotateCw,
      endpoint: '/api/pdf/rotate',
      isPremium: false,
      fields: [{ name: 'rotation', type: 'select', options: ['90', '180', '270'], default: '90' }],
      color: 'bg-green-50 border-green-200 hover:bg-green-100'
    },
    {
      id: 'remove-pages',
      title: 'Remove Pages',
      description: 'Remove specific pages from PDF',
      icon: Trash2,
      endpoint: '/api/pdf/remove-pages',
      isPremium: false,
      fields: [{ name: 'pageNumbers', type: 'text', placeholder: 'e.g., [1,3,5]' }],
      color: 'bg-red-50 border-red-200 hover:bg-red-100'
    },
    {
      id: 'organize',
      title: 'Organize PDF',
      description: 'Reorder PDF pages in any sequence',
      icon: ArrowUpDown,
      endpoint: '/api/pdf/organize',
      isPremium: true,
      fields: [{ name: 'pageOrder', type: 'text', placeholder: 'e.g., [3,1,2,4]' }],
      color: 'bg-purple-50 border-purple-200 hover:bg-purple-100'
    },

    // Row 2
    {
      id: 'compress',
      title: 'Compress PDF',
      description: 'Reduce PDF file size without losing quality',
      icon: Archive,
      endpoint: '/api/pdf/compress',
      isPremium: false,
      color: 'bg-orange-50 border-orange-200 hover:bg-orange-100'
    },
    {
      id: 'grayscale',
      title: 'Grayscale PDF',
      description: 'Convert PDF to grayscale/black and white',
      icon: Palette,
      endpoint: '/api/pdf/grayscale',
      isPremium: false,
      color: 'bg-gray-50 border-gray-200 hover:bg-gray-100'
    },
    {
      id: 'extract-pages',
      title: 'Extract PDF Pages',
      description: 'Extract specific pages from PDF',
      icon: Scissors,
      endpoint: '/api/pdf/extract-pages',
      isPremium: false,
      fields: [{ name: 'pageNumbers', type: 'text', placeholder: 'e.g., [1,3,5]' }],
      color: 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100'
    },
    {
      id: 'repair',
      title: 'Repair PDF',
      description: 'Fix corrupted or damaged PDF files',
      icon: Wrench,
      endpoint: '/api/pdf/repair',
      isPremium: false,
      color: 'bg-emerald-50 border-emerald-200 hover:bg-emerald-100'
    },

    // Row 3 - Image to PDF
    {
      id: 'jpg-to-pdf',
      title: 'JPG To PDF',
      description: 'Convert JPG images to PDF documents',
      icon: Image,
      endpoint: '/api/pdf/jpg-to-pdf',
      isPremium: false,
      acceptMultiple: true,
      accept: '.jpg,.jpeg',
      color: 'bg-pink-50 border-pink-200 hover:bg-pink-100'
    },
    {
      id: 'png-to-pdf',
      title: 'PNG To PDF',
      description: 'Convert PNG images to PDF documents',
      icon: FileImage,
      endpoint: '/api/pdf/png-to-pdf',
      isPremium: false,
      acceptMultiple: true,
      accept: '.png',
      color: 'bg-indigo-50 border-indigo-200 hover:bg-indigo-100'
    },
    {
      id: 'bmp-to-pdf',
      title: 'BMP To PDF',
      description: 'Convert BMP images to PDF documents',
      icon: Monitor,
      endpoint: '/api/pdf/bmp-to-pdf',
      isPremium: false,
      acceptMultiple: true,
      accept: '.bmp',
      color: 'bg-cyan-50 border-cyan-200 hover:bg-cyan-100'
    },
    {
      id: 'tiff-to-pdf',
      title: 'TIFF To PDF',
      description: 'Convert TIFF images to PDF documents',
      icon: Smartphone,
      endpoint: '/api/pdf/tiff-to-pdf',
      isPremium: false,
      acceptMultiple: true,
      accept: '.tiff,.tif',
      color: 'bg-lime-50 border-lime-200 hover:bg-lime-100'
    },

    // Row 4 - Document to PDF
    {
      id: 'word-to-pdf',
      title: 'Word to PDF',
      description: 'Convert Word documents to PDF',
      icon: FileText,
      endpoint: '/api/pdf/word-to-pdf',
      isPremium: false,
      accept: '.docx,.doc',
      color: 'bg-blue-50 border-blue-200 hover:bg-blue-100'
    },
    {
      id: 'powerpoint-to-pdf',
      title: 'PowerPoint to PDF',
      description: 'Convert PowerPoint presentations to PDF',
      icon: Presentation,
      endpoint: '/api/pdf/powerpoint-to-pdf',
      isPremium: false,
      accept: '.pptx,.ppt',
      color: 'bg-red-50 border-red-200 hover:bg-red-100'
    },
    {
      id: 'txt-to-pdf',
      title: 'TXT To PDF',
      description: 'Convert text files to PDF documents',
      icon: Type,
      endpoint: '/api/pdf/txt-to-pdf',
      isPremium: false,
      accept: '.txt',
      color: 'bg-slate-50 border-slate-200 hover:bg-slate-100'
    },
    {
      id: 'excel-to-pdf',
      title: 'Excel To PDF',
      description: 'Convert Excel spreadsheets to PDF',
      icon: FileSpreadsheet,
      endpoint: '/api/pdf/excel-to-pdf',
      isPremium: false,
      accept: '.xlsx,.xls',
      color: 'bg-green-50 border-green-200 hover:bg-green-100'
    },

    // Row 5 - PDF to Images
    {
      id: 'pdf-to-jpg',
      title: 'PDF To JPG',
      description: 'Convert PDF pages to JPG images',
      icon: Image,
      endpoint: '/api/pdf/pdf-to-jpg',
      isPremium: false,
      color: 'bg-pink-50 border-pink-200 hover:bg-pink-100'
    },
    {
      id: 'pdf-to-png',
      title: 'PDF To PNG',
      description: 'Convert PDF pages to PNG images',
      icon: FileImage,
      endpoint: '/api/pdf/pdf-to-png',
      isPremium: false,
      color: 'bg-indigo-50 border-indigo-200 hover:bg-indigo-100'
    },
    {
      id: 'pdf-to-bmp',
      title: 'PDF To BMP',
      description: 'Convert PDF pages to BMP images',
      icon: Monitor,
      endpoint: '/api/pdf/pdf-to-bmp',
      isPremium: false,
      color: 'bg-cyan-50 border-cyan-200 hover:bg-cyan-100'
    },
    {
      id: 'pdf-to-tiff',
      title: 'PDF To TIFF',
      description: 'Convert PDF pages to TIFF images',
      icon: Smartphone,
      endpoint: '/api/pdf/pdf-to-tiff',
      isPremium: false,
      color: 'bg-lime-50 border-lime-200 hover:bg-lime-100'
    },

    // Row 6 - PDF to Documents
    {
      id: 'pdf-to-word',
      title: 'PDF To Word',
      description: 'Convert PDF to editable Word documents',
      icon: FileText,
      endpoint: '/api/pdf/pdf-to-word',
      isPremium: false,
      color: 'bg-blue-50 border-blue-200 hover:bg-blue-100'
    },
    {
      id: 'pdf-to-powerpoint',
      title: 'PDF To PowerPoint',
      description: 'Convert PDF to PowerPoint presentations',
      icon: Presentation,
      endpoint: '/api/pdf/pdf-to-powerpoint',
      isPremium: false,
      color: 'bg-red-50 border-red-200 hover:bg-red-100'
    },
    {
      id: 'pdf-to-txt',
      title: 'PDF To TXT',
      description: 'Extract text from PDF to text files',
      icon: Type,
      endpoint: '/api/pdf/pdf-to-txt',
      isPremium: false,
      color: 'bg-slate-50 border-slate-200 hover:bg-slate-100'
    },
    {
      id: 'pdf-to-zip',
      title: 'PDF To ZIP',
      description: 'Convert PDF to ZIP archive with images',
      icon: Archive,
      endpoint: '/api/pdf/pdf-to-zip',
      isPremium: false,
      color: 'bg-orange-50 border-orange-200 hover:bg-orange-100'
    },

    // Row 7 - Security
    {
      id: 'protect',
      title: 'Protect PDF',
      description: 'Add password and security to PDF',
      icon: Lock,
      endpoint: '/api/pdf/protect',
      isPremium: false,
      fields: [{ name: 'password', type: 'password', placeholder: 'Enter password' }],
      color: 'bg-red-50 border-red-200 hover:bg-red-100'
    },
    {
      id: 'unlock',
      title: 'Unlock PDF',
      description: 'Remove password from PDF files',
      icon: Unlock,
      endpoint: '/api/pdf/unlock',
      isPremium: false,
      fields: [{ name: 'password', type: 'password', placeholder: 'Current password (optional)' }],
      color: 'bg-green-50 border-green-200 hover:bg-green-100'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">PDF Zone</h1>
          <p className="text-muted-foreground">
            Complete PDF toolkit with 24 professional tools for all your document needs
          </p>
        </div>
        {!isPremium && (
          <Link href="/subscribe">
            <Badge variant="outline" className="text-primary border-primary hover:bg-primary/10 cursor-pointer transition-colors">
              <Crown className="w-4 h-4 mr-1" />
              Upgrade for Full Access
            </Badge>
          </Link>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {pdfTools.map((tool) => {
          const Icon = tool.icon;
          const isLocked = tool.isPremium && !isPremium;

          return (
            <Card
              key={tool.id}
              className={`relative transition-all duration-200 cursor-pointer ${tool.color} ${
                isLocked ? 'opacity-60' : 'hover:shadow-md'
              }`}
              onClick={() => !isLocked && setSelectedTool(selectedTool === tool.id ? null : tool.id)}
            >
              {tool.isPremium && (
                <div className="absolute top-2 right-2">
                  <Crown className="h-4 w-4 text-yellow-500" />
                </div>
              )}

              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-white/50">
                    <Icon className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-sm font-semibold">{tool.title}</CardTitle>
                  </div>
                </div>
                <CardDescription className="text-xs text-muted-foreground">
                  {tool.description}
                </CardDescription>
              </CardHeader>

              {selectedTool === tool.id && !isLocked && (
                <CardContent className="pt-0">
                  <form onSubmit={async (e) => {
                    e.preventDefault();
                    const formData = new FormData(e.target as HTMLFormElement);
                    await processFile(tool.endpoint, formData);
                    setSelectedTool(null);
                  }}>
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor={`${tool.id}-file`} className="text-xs">
                          {tool.acceptMultiple ? 'Select Files' : 'Select File'}
                        </Label>
                        <Input
                          id={`${tool.id}-file`}
                          name={tool.acceptMultiple ? 'images' : 'file'}
                          type="file"
                          accept={tool.accept || '.pdf'}
                          multiple={tool.acceptMultiple}
                          required
                          className="mt-1 text-xs"
                        />
                      </div>

                      {tool.fields?.map((field, index) => (
                        <div key={index}>
                          <Label htmlFor={`${tool.id}-${field.name}`} className="text-xs">
                            {field.name}
                          </Label>
                          {field.type === 'select' ? (
                            <select
                              id={`${tool.id}-${field.name}`}
                              name={field.name}
                              className="w-full mt-1 px-2 py-1 text-xs border rounded"
                              defaultValue={field.default}
                            >
                              {field.options?.map(option => (
                                <option key={option} value={option}>{option}</option>
                              ))}
                            </select>
                          ) : (
                            <Input
                              id={`${tool.id}-${field.name}`}
                              name={field.name}
                              type={field.type || 'text'}
                              placeholder={field.placeholder}
                              className="mt-1 text-xs"
                            />
                          )}
                        </div>
                      ))}

                      <Button
                        type="submit"
                        size="sm"
                        className="w-full text-xs"
                        disabled={isProcessing}
                      >
                        {isProcessing ? (
                          <>
                            <Upload className="h-3 w-3 mr-1 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Download className="h-3 w-3 mr-1" />
                            Process
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              )}

              {isLocked && (
                <CardContent className="pt-0">
                  <div className="text-center">
                    <Lock className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-xs text-muted-foreground">Premium Feature</p>
                    <Link href="/subscribe">
                      <Button size="sm" variant="outline" className="mt-2 text-xs hover:bg-primary/10">
                        <Crown className="h-3 w-3 mr-1" />
                        Upgrade Now
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>
    </div>
  );
}
