import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
  Eye,
  Palette,
  CreditCard,
  Check,
  ShoppingCart,
  Truck
} from "lucide-react";

interface CheckoutPageBuilderProps {
  formData: {
    name: string;
    slug: string;
    title: string;
    description: string;
    price: string;
    currency: string;
    productName: string;
    features: string[];
    customerFields: {
      requireName: boolean;
      requireEmail: boolean;
      requirePhone: boolean;
      requireAddress: boolean;
      customFields: Array<{
        label: string;
        type: string;
        required: boolean;
      }>;
    };
    smtpConfigId: string;
    theme: {
      primaryColor: string;
      secondaryColor: string;
      backgroundColor: string;
      textColor: string;
      fontFamily: string;
      layout: string;
    };
    paymentGateways: {
      stripe: boolean;
      paypal: boolean;
      paypalStandard: boolean;
      paddle: boolean;
      sumup: boolean;
      wise: boolean;
      payoneer: boolean;
      bankTransfer: boolean;
      cod: boolean;
    };
  };
  onFormChange: (formData: any) => void;
}

export default function CheckoutPageBuilder({ formData, onFormChange }: CheckoutPageBuilderProps) {
  const handleNameChange = (name: string) => {
    const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
    onFormChange({
      ...formData,
      name,
      slug
    });
  };

  const handleThemeChange = (field: string, value: string) => {
    onFormChange({
      ...formData,
      theme: {
        ...formData.theme,
        [field]: value
      }
    });
  };

  const handlePaymentGatewayChange = (gateway: string, enabled: boolean) => {
    onFormChange({
      ...formData,
      paymentGateways: {
        ...formData.paymentGateways,
        [gateway]: enabled
      }
    });
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Page Builder
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="customer">Customer</TabsTrigger>
              <TabsTrigger value="design">Design</TabsTrigger>
              <TabsTrigger value="payment">Payment</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div>
                <Label htmlFor="page-name">Page Name *</Label>
                <Input
                  id="page-name"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="Premium Plan Checkout"
                  required
                />
              </div>

              <div>
                <Label htmlFor="product-name">Product Name *</Label>
                <Input
                  id="product-name"
                  value={formData.productName || "PDF Tools Premium"}
                  onChange={(e) => onFormChange({ ...formData, productName: e.target.value })}
                  placeholder="PDF Tools Premium"
                  required
                />
                <p className="text-sm text-muted-foreground mt-1">
                  This will be displayed as the main product being sold
                </p>
              </div>

              <div>
                <Label htmlFor="page-slug">URL Slug</Label>
                <Input
                  id="page-slug"
                  value={formData.slug}
                  onChange={(e) => onFormChange({...formData, slug: e.target.value})}
                  placeholder="premium-plan"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Will be available at: /checkout/{formData.slug}
                </p>
              </div>

              <div>
                <Label htmlFor="page-title">Page Title *</Label>
                <Input
                  id="page-title"
                  value={formData.title}
                  onChange={(e) => onFormChange({...formData, title: e.target.value})}
                  placeholder="Upgrade to Premium"
                  required
                />
              </div>

              <div>
                <Label htmlFor="page-description">Description</Label>
                <Textarea
                  id="page-description"
                  value={formData.description}
                  onChange={(e) => onFormChange({...formData, description: e.target.value})}
                  placeholder="Unlock all features and process unlimited PDFs"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="price">Price *</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    value={formData.price}
                    onChange={(e) => onFormChange({...formData, price: e.target.value})}
                    placeholder="48.00"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <Select
                    value={formData.currency}
                    onValueChange={(value) => onFormChange({...formData, currency: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="customer" className="space-y-4">
              <div>
                <Label>Customer Information Fields</Label>
                <p className="text-sm text-muted-foreground mb-4">
                  Choose which information to collect from customers during checkout
                </p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Customer Name</Label>
                    <p className="text-sm text-muted-foreground">Collect customer's full name</p>
                  </div>
                  <Switch
                    checked={formData.customerFields?.requireName || true}
                    onCheckedChange={(checked) =>
                      onFormChange({
                        ...formData,
                        customerFields: {
                          ...formData.customerFields,
                          requireName: checked
                        }
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Email Address</Label>
                    <p className="text-sm text-muted-foreground">Required for sending receipts</p>
                  </div>
                  <Switch
                    checked={formData.customerFields?.requireEmail || true}
                    onCheckedChange={(checked) =>
                      onFormChange({
                        ...formData,
                        customerFields: {
                          ...formData.customerFields,
                          requireEmail: checked
                        }
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Phone Number</Label>
                    <p className="text-sm text-muted-foreground">Optional contact information</p>
                  </div>
                  <Switch
                    checked={formData.customerFields?.requirePhone || false}
                    onCheckedChange={(checked) =>
                      onFormChange({
                        ...formData,
                        customerFields: {
                          ...formData.customerFields,
                          requirePhone: checked
                        }
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Billing Address</Label>
                    <p className="text-sm text-muted-foreground">Collect full billing address</p>
                  </div>
                  <Switch
                    checked={formData.customerFields?.requireAddress || false}
                    onCheckedChange={(checked) =>
                      onFormChange({
                        ...formData,
                        customerFields: {
                          ...formData.customerFields,
                          requireAddress: checked
                        }
                      })
                    }
                  />
                </div>
              </div>

              <div className="border-t pt-4">
                <Label>Email Configuration</Label>
                <p className="text-sm text-muted-foreground mb-3">
                  Select SMTP server for sending confirmation emails
                </p>
                <Select
                  value={formData.smtpConfigId || ""}
                  onValueChange={(value) =>
                    onFormChange({ ...formData, smtpConfigId: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select SMTP Configuration" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">Main SMTP (smtp.gmail.com)</SelectItem>
                    <SelectItem value="2">Support SMTP (smtp.office365.com)</SelectItem>
                    <SelectItem value="3">Marketing SMTP (smtp.sendgrid.net)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground mt-1">
                  SMTP servers are configured in Admin Settings
                </p>
              </div>
            </TabsContent>

            <TabsContent value="design" className="space-y-4">
              <div>
                <Label className="mb-3 block">Brand Colors</Label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="primary-color">Primary Color</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="primary-color"
                        type="color"
                        value={formData.theme.primaryColor}
                        onChange={(e) => handleThemeChange('primaryColor', e.target.value)}
                        className="w-12 h-10 p-1"
                      />
                      <Input
                        value={formData.theme.primaryColor}
                        onChange={(e) => handleThemeChange('primaryColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="secondary-color">Secondary Color</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="secondary-color"
                        type="color"
                        value={formData.theme.secondaryColor}
                        onChange={(e) => handleThemeChange('secondaryColor', e.target.value)}
                        className="w-12 h-10 p-1"
                      />
                      <Input
                        value={formData.theme.secondaryColor}
                        onChange={(e) => handleThemeChange('secondaryColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="font-family">Typography</Label>
                <Select
                  value={formData.theme.fontFamily}
                  onValueChange={(value) => handleThemeChange('fontFamily', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Inter">Inter</SelectItem>
                    <SelectItem value="SF Pro Display">SF Pro Display</SelectItem>
                    <SelectItem value="Roboto">Roboto</SelectItem>
                    <SelectItem value="Open Sans">Open Sans</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="layout">Layout Style</Label>
                <Select
                  value={formData.theme.layout}
                  onValueChange={(value) => handleThemeChange('layout', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="centered">Centered</SelectItem>
                    <SelectItem value="split">Split Screen</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="payment" className="space-y-4">
              <div>
                <Label className="mb-3 block">Payment Gateways</Label>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CreditCard className="h-4 w-4" />
                      <span>Stripe</span>
                    </div>
                    <Switch
                      checked={formData.paymentGateways.stripe}
                      onCheckedChange={(checked) => handlePaymentGatewayChange('stripe', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CreditCard className="h-4 w-4" />
                      <span>PayPal</span>
                    </div>
                    <Switch
                      checked={formData.paymentGateways.paypal}
                      onCheckedChange={(checked) => handlePaymentGatewayChange('paypal', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Truck className="h-4 w-4" />
                      <span>Cash on Delivery</span>
                    </div>
                    <Switch
                      checked={formData.paymentGateways.cod}
                      onCheckedChange={(checked) => handlePaymentGatewayChange('cod', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CreditCard className="h-4 w-4" />
                      <span>PayPal Standard</span>
                    </div>
                    <Switch
                      checked={formData.paymentGateways.paypalStandard}
                      onCheckedChange={(checked) => handlePaymentGatewayChange('paypalStandard', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CreditCard className="h-4 w-4" />
                      <span>Bank Transfer</span>
                    </div>
                    <Switch
                      checked={formData.paymentGateways.bankTransfer}
                      onCheckedChange={(checked) => handlePaymentGatewayChange('bankTransfer', checked)}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Live Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Live Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg overflow-hidden">
            <div className="bg-muted p-3 border-b">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div className="flex-1 bg-background rounded px-3 py-1 text-xs text-muted-foreground ml-3">
                  checkout.pdftools.com/{formData.slug || 'your-page'}
                </div>
              </div>
            </div>

            <div
              className="p-8"
              style={{
                backgroundColor: formData.theme.backgroundColor,
                color: formData.theme.textColor,
                fontFamily: formData.theme.fontFamily,
              }}
            >
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold mb-2">
                  {formData.title || "Page Title"}
                </h2>
                <p className="text-muted-foreground">
                  {formData.description || "Page description"}
                </p>
              </div>

              <div className="bg-muted rounded-lg p-6 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <span className="font-medium">{formData.name || "Product Name"}</span>
                  <span className="text-2xl font-bold">
                    {formData.currency} {formData.price || "0.00"}
                  </span>
                </div>

                <div className="space-y-2 text-sm">
                  {formData.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-500" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                {formData.paymentGateways.stripe && (
                  <Button
                    className="w-full"
                    style={{ backgroundColor: formData.theme.primaryColor }}
                  >
                    Pay with Stripe
                  </Button>
                )}
                {formData.paymentGateways.paypal && (
                  <Button className="w-full bg-blue-600 hover:bg-blue-700">
                    Pay with PayPal
                  </Button>
                )}
                {formData.paymentGateways.paypalStandard && (
                  <Button className="w-full bg-blue-600 hover:bg-blue-700">
                    Pay with PayPal Standard
                  </Button>
                )}
                {formData.paymentGateways.bankTransfer && (
                  <Button variant="outline" className="w-full">
                    Bank Transfer
                  </Button>
                )}
                {formData.paymentGateways.cod && (
                  <Button variant="outline" className="w-full">
                    <Truck className="h-4 w-4 mr-2" />
                    Cash on Delivery
                  </Button>
                )}
              </div>

              <p className="text-xs text-center mt-6 text-muted-foreground">
                Secure payment • 30-day money-back guarantee
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
