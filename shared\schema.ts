import { mysqlTable, text, serial, int, boolean, timestamp, decimal, json } from "drizzle-orm/mysql-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = mysqlTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  role: text("role").notNull().default("user"), // user, admin
  isPremium: boolean("is_premium").notNull().default(false),
  premiumExpiresAt: timestamp("premium_expires_at"),
  usageLimit: int("usage_limit").notNull().default(5), // Free tier limit
  usageCount: int("usage_count").notNull().default(0),
  stripeCustomerId: text("stripe_customer_id"),
  stripeSubscriptionId: text("stripe_subscription_id"),
  apiKey: text("api_key"),
  customTheme: json("custom_theme"),
  emailVerified: boolean("email_verified").notNull().default(false),
  emailVerificationToken: text("email_verification_token"),
  passwordResetToken: text("password_reset_token"),
  passwordResetExpires: timestamp("password_reset_expires"),
  twoFactorSecret: text("two_factor_secret"),
  twoFactorEnabled: boolean("two_factor_enabled").notNull().default(false),
  lastLoginAt: timestamp("last_login_at"),
  loginAttempts: int("login_attempts").notNull().default(0),
  lockedUntil: timestamp("locked_until"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const pdfOperations = mysqlTable("pdf_operations", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  operation: text("operation").notNull(), // merge, split, compress, convert, watermark, password
  fileName: text("file_name").notNull(),
  fileSize: int("file_size").notNull(),
  status: text("status").notNull().default("processing"), // processing, completed, failed
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const checkoutPages = mysqlTable("checkout_pages", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(),
  title: text("title").notNull(),
  description: text("description"),
  productName: text("product_name").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  currency: text("currency").notNull().default("USD"),
  smtpConfigId: int("smtp_config_id").references(() => smtpConfigs.id),
  features: json("features"), // Product features list
  customerFields: json("customer_fields"), // Customer form fields configuration
  theme: json("theme").notNull(), // colors, fonts, layout, styling
  paymentGateways: json("payment_gateways").notNull(), // stripe, paypal, paddle, sumup, wise, payoneer, bankTransfer
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const smtpConfigs = mysqlTable("smtp_configs", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  host: text("host").notNull(),
  port: int("port").notNull(),
  username: text("username").notNull(),
  password: text("password").notNull(),
  secure: boolean("secure").notNull().default(true),
  fromEmail: text("from_email").notNull(),
  fromName: text("from_name").notNull(),
  isDefault: boolean("is_default").notNull().default(false),
  routingRules: json("routing_rules"), // For checkout page routing
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const emailTemplates = mysqlTable("email_templates", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  type: text("type").notNull(), // payment_success, payment_failed, subscription_renewed, etc.
  subject: text("subject").notNull(),
  htmlContent: text("html_content").notNull(),
  textContent: text("text_content"),
  variables: json("variables"), // Available template variables
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const webhooks = mysqlTable("webhooks", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  url: text("url").notNull(),
  events: json("events").notNull(), // Array of event types to listen for
  secret: text("secret").notNull(),
  isActive: boolean("is_active").notNull().default(true),
  lastTriggered: timestamp("last_triggered"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const siteConfig = mysqlTable("site_config", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  value: json("value").notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const payments = mysqlTable("payments", {
  id: serial("id").primaryKey(),
  userId: int("user_id").references(() => users.id),
  checkoutPageId: int("checkout_page_id").references(() => checkoutPages.id),
  amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
  currency: text("currency").notNull(),
  gateway: text("gateway").notNull(), // stripe, paypal, etc
  gatewayTransactionId: text("gateway_transaction_id"),
  status: text("status").notNull().default("pending"), // pending, completed, failed, refunded
  customerData: json("customer_data"), // Customer information from checkout
  invoiceNumber: text("invoice_number").unique(),
  invoiceGenerated: boolean("invoice_generated").notNull().default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const invoices = mysqlTable("invoices", {
  id: serial("id").primaryKey(),
  invoiceNumber: text("invoice_number").notNull().unique(),
  paymentId: int("payment_id").references(() => payments.id),
  customerName: text("customer_name").notNull(),
  customerEmail: text("customer_email").notNull(),
  customerPhone: text("customer_phone"),
  customerAddress: text("customer_address"),
  companyName: text("company_name"),
  taxId: text("tax_id"),
  productName: text("product_name").notNull(),
  productDescription: text("product_description"),
  quantity: int("quantity").notNull().default(1),
  unitPrice: decimal("unit_price", { precision: 10, scale: 2 }).notNull(),
  subtotal: decimal("subtotal", { precision: 10, scale: 2 }).notNull(),
  taxRate: decimal("tax_rate", { precision: 5, scale: 4 }).default("0.0000"),
  taxAmount: decimal("tax_amount", { precision: 10, scale: 2 }).default("0.00"),
  total: decimal("total", { precision: 10, scale: 2 }).notNull(),
  currency: text("currency").notNull(),
  dueDate: timestamp("due_date"),
  paidDate: timestamp("paid_date"),
  status: text("status").notNull().default("draft"), // draft, sent, paid, overdue, cancelled
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const loginHistory = mysqlTable("login_history", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  ipAddress: text("ip_address").notNull(),
  userAgent: text("user_agent"),
  success: boolean("success").notNull(),
  failureReason: text("failure_reason"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const apiKeys = mysqlTable("api_keys", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  keyHash: text("key_hash").notNull(),
  lastUsed: timestamp("last_used"),
  usageCount: int("usage_count").notNull().default(0),
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const rateLimits = mysqlTable("rate_limits", {
  id: serial("id").primaryKey(),
  identifier: text("identifier").notNull(), // IP address or user ID
  endpoint: text("endpoint").notNull(),
  requests: int("requests").notNull().default(1),
  windowStart: timestamp("window_start").defaultNow().notNull(),
});

export const systemSettings = mysqlTable("system_settings", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  value: json("value").notNull(),
  description: text("description"),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const pages = mysqlTable("pages", {
  id: serial("id").primaryKey(),
  slug: text("slug").notNull().unique(),
  title: text("title").notNull(),
  content: text("content").notNull(),
  metaDescription: text("meta_description"),
  metaKeywords: text("meta_keywords"),
  isPublished: boolean("is_published").notNull().default(true),
  showInFooter: boolean("show_in_footer").notNull().default(false),
  footerSection: text("footer_section"), // 'product', 'support', 'legal', 'company'
  sortOrder: int("sort_order").notNull().default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  email: true,
  password: true,
});

export const insertPdfOperationSchema = createInsertSchema(pdfOperations).pick({
  operation: true,
  fileName: true,
  fileSize: true,
});

export const insertCheckoutPageSchema = createInsertSchema(checkoutPages).pick({
  name: true,
  slug: true,
  title: true,
  description: true,
  productName: true,
  price: true,
  currency: true,
  smtpConfigId: true,
  features: true,
  customerFields: true,
  theme: true,
  paymentGateways: true,
});

export const insertSmtpConfigSchema = createInsertSchema(smtpConfigs).pick({
  name: true,
  host: true,
  port: true,
  username: true,
  password: true,
  secure: true,
  fromEmail: true,
  fromName: true,
  isDefault: true,
  routingRules: true,
});

export const insertEmailTemplateSchema = createInsertSchema(emailTemplates).pick({
  name: true,
  type: true,
  subject: true,
  htmlContent: true,
  textContent: true,
  variables: true,
  isActive: true,
});

export const insertWebhookSchema = createInsertSchema(webhooks).pick({
  name: true,
  url: true,
  events: true,
  secret: true,
  isActive: true,
});

export const insertSiteConfigSchema = createInsertSchema(siteConfig).pick({
  key: true,
  value: true,
});

export const insertPaymentSchema = createInsertSchema(payments).pick({
  checkoutPageId: true,
  amount: true,
  currency: true,
  gateway: true,
  gatewayTransactionId: true,
  customerData: true,
});

export const insertInvoiceSchema = createInsertSchema(invoices).pick({
  invoiceNumber: true,
  paymentId: true,
  customerName: true,
  customerEmail: true,
  customerPhone: true,
  customerAddress: true,
  companyName: true,
  taxId: true,
  productName: true,
  productDescription: true,
  quantity: true,
  unitPrice: true,
  subtotal: true,
  taxRate: true,
  taxAmount: true,
  total: true,
  currency: true,
  dueDate: true,
  notes: true,
});

export const updateInvoiceSchema = createInsertSchema(invoices).pick({
  customerName: true,
  customerEmail: true,
  customerPhone: true,
  customerAddress: true,
  companyName: true,
  taxId: true,
  productName: true,
  productDescription: true,
  quantity: true,
  unitPrice: true,
  subtotal: true,
  taxRate: true,
  taxAmount: true,
  total: true,
  currency: true,
  dueDate: true,
  paidDate: true,
  status: true,
  notes: true,
}).partial();

// Additional schemas for security features
export const insertApiKeySchema = createInsertSchema(apiKeys).pick({
  name: true,
});

export const insertPageSchema = createInsertSchema(pages).pick({
  slug: true,
  title: true,
  content: true,
  metaDescription: true,
  metaKeywords: true,
  isPublished: true,
  showInFooter: true,
  footerSection: true,
  sortOrder: true,
});

export const updatePageSchema = createInsertSchema(pages).pick({
  title: true,
  content: true,
  metaDescription: true,
  metaKeywords: true,
  isPublished: true,
  showInFooter: true,
  footerSection: true,
  sortOrder: true,
}).partial();

export const updateUserSchema = z.object({
  username: z.string().min(3).optional(),
  email: z.string().email().optional(),
  currentPassword: z.string().optional(),
  newPassword: z.string().min(6).optional(),
});

export const passwordResetSchema = z.object({
  email: z.string().email(),
});

export const passwordResetConfirmSchema = z.object({
  token: z.string(),
  password: z.string().min(6),
});

export const twoFactorSetupSchema = z.object({
  secret: z.string(),
  token: z.string().length(6),
});

export const twoFactorVerifySchema = z.object({
  token: z.string().length(6),
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertPdfOperation = z.infer<typeof insertPdfOperationSchema>;
export type PdfOperation = typeof pdfOperations.$inferSelect;
export type InsertCheckoutPage = z.infer<typeof insertCheckoutPageSchema>;
export type CheckoutPage = typeof checkoutPages.$inferSelect;
export type InsertSmtpConfig = z.infer<typeof insertSmtpConfigSchema>;
export type SmtpConfig = typeof smtpConfigs.$inferSelect;
export type InsertEmailTemplate = z.infer<typeof insertEmailTemplateSchema>;
export type EmailTemplate = typeof emailTemplates.$inferSelect;
export type InsertWebhook = z.infer<typeof insertWebhookSchema>;
export type Webhook = typeof webhooks.$inferSelect;
export type InsertSiteConfig = z.infer<typeof insertSiteConfigSchema>;
export type SiteConfig = typeof siteConfig.$inferSelect;
export type InsertPayment = z.infer<typeof insertPaymentSchema>;
export type Payment = typeof payments.$inferSelect;
export type LoginHistory = typeof loginHistory.$inferSelect;
export type ApiKey = typeof apiKeys.$inferSelect;
export type InsertApiKey = z.infer<typeof insertApiKeySchema>;
export type RateLimit = typeof rateLimits.$inferSelect;
export type SystemSetting = typeof systemSettings.$inferSelect;
export type Page = typeof pages.$inferSelect;
export type InsertPage = z.infer<typeof insertPageSchema>;
export type UpdatePage = z.infer<typeof updatePageSchema>;
export type Invoice = typeof invoices.$inferSelect;
export type InsertInvoice = z.infer<typeof insertInvoiceSchema>;
export type UpdateInvoice = z.infer<typeof updateInvoiceSchema>;
