import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  FileText,
  Eye,
  EyeOff,
  Globe,
  Link
} from "lucide-react";

interface Page {
  id: number;
  slug: string;
  title: string;
  content: string;
  metaDescription?: string;
  metaKeywords?: string;
  isPublished: boolean;
  showInFooter: boolean;
  footerSection?: string;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

interface PageFormData {
  slug: string;
  title: string;
  content: string;
  metaDescription: string;
  metaKeywords: string;
  isPublished: boolean;
  showInFooter: boolean;
  footerSection: string;
  sortOrder: number;
}

export default function PageManager() {
  const [pages, setPages] = useState<Page[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingPage, setEditingPage] = useState<Page | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<PageFormData>({
    slug: "",
    title: "",
    content: "",
    metaDescription: "",
    metaKeywords: "",
    isPublished: true,
    showInFooter: false,
    footerSection: "none",
    sortOrder: 0
  });
  const { toast } = useToast();

  const footerSections = [
    { value: "none", label: "None" },
    { value: "product", label: "Product" },
    { value: "support", label: "Support" },
    { value: "legal", label: "Legal" },
    { value: "company", label: "Company" }
  ];

  useEffect(() => {
    fetchPages();
  }, []);

  const fetchPages = async () => {
    try {
      const response = await fetch("/api/admin/pages", {
        credentials: "include"
      });
      if (response.ok) {
        const data = await response.json();
        setPages(data);
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch pages",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error", 
        description: "Failed to fetch pages",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const url = editingPage 
        ? `/api/admin/pages/${editingPage.slug}`
        : "/api/admin/pages";
      
      const method = editingPage ? "PUT" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json"
        },
        credentials: "include",
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: editingPage ? "Page updated successfully" : "Page created successfully"
        });
        
        setShowForm(false);
        setEditingPage(null);
        resetForm();
        fetchPages();
      } else {
        const error = await response.json();
        toast({
          title: "Error",
          description: error.message || "Failed to save page",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save page",
        variant: "destructive"
      });
    }
  };

  const handleEdit = (page: Page) => {
    setEditingPage(page);
    setFormData({
      slug: page.slug,
      title: page.title,
      content: page.content,
      metaDescription: page.metaDescription || "",
      metaKeywords: page.metaKeywords || "",
      isPublished: page.isPublished,
      showInFooter: page.showInFooter,
      footerSection: page.footerSection || "none",
      sortOrder: page.sortOrder
    });
    setShowForm(true);
  };

  const handleDelete = async (slug: string) => {
    if (!confirm("Are you sure you want to delete this page?")) return;

    try {
      const response = await fetch(`/api/admin/pages/${slug}`, {
        method: "DELETE",
        credentials: "include"
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Page deleted successfully"
        });
        fetchPages();
      } else {
        toast({
          title: "Error",
          description: "Failed to delete page",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete page",
        variant: "destructive"
      });
    }
  };

  const resetForm = () => {
    setFormData({
      slug: "",
      title: "",
      content: "",
      metaDescription: "",
      metaKeywords: "",
      isPublished: true,
      showInFooter: false,
      footerSection: "none",
      sortOrder: 0
    });
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingPage(null);
    resetForm();
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: editingPage ? prev.slug : generateSlug(title)
    }));
  };

  if (loading) {
    return <div className="flex justify-center p-8">Loading pages...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Page Management</h2>
        <Button onClick={() => setShowForm(true)} disabled={showForm}>
          <Plus className="w-4 h-4 mr-2" />
          Add New Page
        </Button>
      </div>

      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingPage ? "Edit Page" : "Create New Page"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleTitleChange(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="slug">Slug</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                    required
                    disabled={!!editingPage}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="content">Content (Markdown)</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  rows={10}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="metaDescription">Meta Description</Label>
                  <Textarea
                    id="metaDescription"
                    value={formData.metaDescription}
                    onChange={(e) => setFormData(prev => ({ ...prev, metaDescription: e.target.value }))}
                    rows={2}
                  />
                </div>
                <div>
                  <Label htmlFor="metaKeywords">Meta Keywords</Label>
                  <Input
                    id="metaKeywords"
                    value={formData.metaKeywords}
                    onChange={(e) => setFormData(prev => ({ ...prev, metaKeywords: e.target.value }))}
                    placeholder="keyword1, keyword2, keyword3"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isPublished"
                    checked={formData.isPublished}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isPublished: checked }))}
                  />
                  <Label htmlFor="isPublished">Published</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="showInFooter"
                    checked={formData.showInFooter}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, showInFooter: checked }))}
                  />
                  <Label htmlFor="showInFooter">Show in Footer</Label>
                </div>
                <div>
                  <Label htmlFor="sortOrder">Sort Order</Label>
                  <Input
                    id="sortOrder"
                    type="number"
                    value={formData.sortOrder}
                    onChange={(e) => setFormData(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 0 }))}
                  />
                </div>
              </div>

              {formData.showInFooter && (
                <div>
                  <Label htmlFor="footerSection">Footer Section</Label>
                  <Select
                    value={formData.footerSection}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, footerSection: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select footer section" />
                    </SelectTrigger>
                    <SelectContent>
                      {footerSections.map((section) => (
                        <SelectItem key={section.value} value={section.value}>
                          {section.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="flex space-x-2">
                <Button type="submit">
                  <Save className="w-4 h-4 mr-2" />
                  {editingPage ? "Update Page" : "Create Page"}
                </Button>
                <Button type="button" variant="outline" onClick={handleCancel}>
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4">
        {pages.map((page) => (
          <Card key={page.id}>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-lg font-semibold">{page.title}</h3>
                    <Badge variant={page.isPublished ? "default" : "secondary"}>
                      {page.isPublished ? (
                        <>
                          <Eye className="w-3 h-3 mr-1" />
                          Published
                        </>
                      ) : (
                        <>
                          <EyeOff className="w-3 h-3 mr-1" />
                          Draft
                        </>
                      )}
                    </Badge>
                    {page.showInFooter && (
                      <Badge variant="outline">
                        <Link className="w-3 h-3 mr-1" />
                        Footer
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                    /{page.slug}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {page.metaDescription || "No description"}
                  </p>
                  {page.footerSection && (
                    <p className="text-xs text-gray-400 mt-1">
                      Footer section: {page.footerSection}
                    </p>
                  )}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(`/${page.slug}`, '_blank')}
                    disabled={!page.isPublished}
                  >
                    <Globe className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(page)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(page.slug)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {pages.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-300 mb-2">
              No pages found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              Create your first page to get started
            </p>
            <Button onClick={() => setShowForm(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create First Page
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
