CREATE TABLE `api_keys` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`user_id` int NOT NULL,
	`name` text NOT NULL,
	`key_hash` text NOT NULL,
	`last_used` timestamp,
	`usage_count` int NOT NULL DEFAULT 0,
	`is_active` boolean NOT NULL DEFAULT true,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `api_keys_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `checkout_pages` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`user_id` int NOT NULL,
	`name` text NOT NULL,
	`slug` text NOT NULL,
	`title` text NOT NULL,
	`description` text,
	`product_name` text NOT NULL,
	`price` decimal(10,2) NOT NULL,
	`currency` text NOT NULL DEFAULT ('USD'),
	`smtp_config_id` int,
	`customer_fields` json,
	`theme` json NOT NULL,
	`payment_gateways` json NOT NULL,
	`is_active` boolean NOT NULL DEFAULT true,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `checkout_pages_id` PRIMARY KEY(`id`),
	CONSTRAINT `checkout_pages_slug_unique` UNIQUE(`slug`)
);
--> statement-breakpoint
CREATE TABLE `email_templates` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`user_id` int NOT NULL,
	`name` text NOT NULL,
	`type` text NOT NULL,
	`subject` text NOT NULL,
	`html_content` text NOT NULL,
	`text_content` text,
	`variables` json,
	`is_active` boolean NOT NULL DEFAULT true,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `email_templates_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `login_history` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`user_id` int NOT NULL,
	`ip_address` text NOT NULL,
	`user_agent` text,
	`success` boolean NOT NULL,
	`failure_reason` text,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `login_history_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `payments` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`user_id` int,
	`checkout_page_id` int,
	`amount` decimal(10,2) NOT NULL,
	`currency` text NOT NULL,
	`gateway` text NOT NULL,
	`gateway_transaction_id` text,
	`status` text NOT NULL DEFAULT ('pending'),
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `payments_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `pdf_operations` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`user_id` int NOT NULL,
	`operation` text NOT NULL,
	`file_name` text NOT NULL,
	`file_size` int NOT NULL,
	`status` text NOT NULL DEFAULT ('processing'),
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `pdf_operations_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `rate_limits` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`identifier` text NOT NULL,
	`endpoint` text NOT NULL,
	`requests` int NOT NULL DEFAULT 1,
	`window_start` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `rate_limits_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `site_config` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`key` text NOT NULL,
	`value` json NOT NULL,
	`updated_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `site_config_id` PRIMARY KEY(`id`),
	CONSTRAINT `site_config_key_unique` UNIQUE(`key`)
);
--> statement-breakpoint
CREATE TABLE `smtp_configs` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`user_id` int NOT NULL,
	`name` text NOT NULL,
	`host` text NOT NULL,
	`port` int NOT NULL,
	`username` text NOT NULL,
	`password` text NOT NULL,
	`secure` boolean NOT NULL DEFAULT true,
	`from_email` text NOT NULL,
	`from_name` text NOT NULL,
	`is_default` boolean NOT NULL DEFAULT false,
	`routing_rules` json,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `smtp_configs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `system_settings` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`key` text NOT NULL,
	`value` json NOT NULL,
	`description` text,
	`updated_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `system_settings_id` PRIMARY KEY(`id`),
	CONSTRAINT `system_settings_key_unique` UNIQUE(`key`)
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`username` text NOT NULL,
	`email` text NOT NULL,
	`password` text NOT NULL,
	`role` text NOT NULL DEFAULT ('user'),
	`is_premium` boolean NOT NULL DEFAULT false,
	`premium_expires_at` timestamp,
	`usage_limit` int NOT NULL DEFAULT 5,
	`usage_count` int NOT NULL DEFAULT 0,
	`stripe_customer_id` text,
	`stripe_subscription_id` text,
	`api_key` text,
	`custom_theme` json,
	`email_verified` boolean NOT NULL DEFAULT false,
	`email_verification_token` text,
	`password_reset_token` text,
	`password_reset_expires` timestamp,
	`two_factor_secret` text,
	`two_factor_enabled` boolean NOT NULL DEFAULT false,
	`last_login_at` timestamp,
	`login_attempts` int NOT NULL DEFAULT 0,
	`locked_until` timestamp,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `users_id` PRIMARY KEY(`id`),
	CONSTRAINT `users_username_unique` UNIQUE(`username`),
	CONSTRAINT `users_email_unique` UNIQUE(`email`)
);
--> statement-breakpoint
CREATE TABLE `webhooks` (
	`id` serial AUTO_INCREMENT NOT NULL,
	`user_id` int NOT NULL,
	`name` text NOT NULL,
	`url` text NOT NULL,
	`events` json NOT NULL,
	`secret` text NOT NULL,
	`is_active` boolean NOT NULL DEFAULT true,
	`last_triggered` timestamp,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `webhooks_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
ALTER TABLE `api_keys` ADD CONSTRAINT `api_keys_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `checkout_pages` ADD CONSTRAINT `checkout_pages_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `checkout_pages` ADD CONSTRAINT `checkout_pages_smtp_config_id_smtp_configs_id_fk` FOREIGN KEY (`smtp_config_id`) REFERENCES `smtp_configs`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `email_templates` ADD CONSTRAINT `email_templates_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `login_history` ADD CONSTRAINT `login_history_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `payments` ADD CONSTRAINT `payments_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `payments` ADD CONSTRAINT `payments_checkout_page_id_checkout_pages_id_fk` FOREIGN KEY (`checkout_page_id`) REFERENCES `checkout_pages`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `pdf_operations` ADD CONSTRAINT `pdf_operations_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `smtp_configs` ADD CONSTRAINT `smtp_configs_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `webhooks` ADD CONSTRAINT `webhooks_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;