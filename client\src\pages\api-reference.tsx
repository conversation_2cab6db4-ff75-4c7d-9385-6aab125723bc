import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Code, 
  Shield, 
  Key,
  Zap,
  BookOpen,
  Globe,
  Clock,
  AlertTriangle
} from "lucide-react";

export default function ApiReference() {
  const endpoints = [
    {
      method: "POST",
      path: "/convert/pdf-to-word",
      description: "Convert PDF to Microsoft Word format",
      parameters: [
        { name: "file", type: "file", required: true, description: "PDF file to convert" },
        { name: "format", type: "string", required: false, description: "Output format (docx, doc)" }
      ]
    },
    {
      method: "POST", 
      path: "/convert/pdf-to-excel",
      description: "Convert PDF tables to Excel format",
      parameters: [
        { name: "file", type: "file", required: true, description: "PDF file to convert" },
        { name: "format", type: "string", required: false, description: "Output format (xlsx, xls)" }
      ]
    },
    {
      method: "POST",
      path: "/convert/pdf-to-images", 
      description: "Convert PDF pages to images",
      parameters: [
        { name: "file", type: "file", required: true, description: "PDF file to convert" },
        { name: "format", type: "string", required: false, description: "Image format (jpg, png)" },
        { name: "dpi", type: "number", required: false, description: "Image resolution (default: 300)" }
      ]
    },
    {
      method: "POST",
      path: "/process/merge",
      description: "Merge multiple PDF files",
      parameters: [
        { name: "files", type: "file[]", required: true, description: "PDF files to merge" }
      ]
    },
    {
      method: "POST",
      path: "/process/split",
      description: "Split PDF into separate files", 
      parameters: [
        { name: "file", type: "file", required: true, description: "PDF file to split" },
        { name: "pages", type: "string", required: true, description: "Page ranges (e.g., '1-3,5,7-9')" }
      ]
    },
    {
      method: "POST",
      path: "/security/encrypt",
      description: "Add password protection to PDF",
      parameters: [
        { name: "file", type: "file", required: true, description: "PDF file to encrypt" },
        { name: "password", type: "string", required: true, description: "Password for encryption" }
      ]
    }
  ];

  const rateLimits = [
    { tier: "Free", requests: "100 requests per hour", description: "Perfect for testing and small projects" },
    { tier: "Premium", requests: "1000 requests per hour", description: "For production applications" },
    { tier: "Enterprise", requests: "Custom limits", description: "Tailored to your needs" }
  ];

  const errorCodes = [
    { code: "400", name: "Bad Request", description: "Invalid parameters or malformed request" },
    { code: "401", name: "Unauthorized", description: "Invalid or missing API key" },
    { code: "429", name: "Too Many Requests", description: "Rate limit exceeded" },
    { code: "500", name: "Internal Server Error", description: "Server error occurred" }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Navigation */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/">
                <span className="text-2xl font-bold text-primary">PDFTools Pro</span>
              </Link>
              <div className="hidden md:block ml-10">
                <div className="flex items-baseline space-x-4">
                  <Link href="/pricing">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Pricing</span>
                  </Link>
                  <Link href="/about">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">About</span>
                  </Link>
                  <Link href="/contact">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Contact</span>
                  </Link>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-4 flex items-center md:ml-6 space-x-4">
                <Link href="/auth">
                  <Button variant="ghost">Login</Button>
                </Link>
                <Link href="/auth">
                  <Button>Sign Up Free</Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/5 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-700 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-primary/10 rounded-full">
              <Code className="h-12 w-12 text-primary" />
            </div>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            API Reference
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            Complete API reference for PDFTools Pro - authentication, endpoints, rate limits, and code examples for developers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth">
              <Button size="lg" className="text-lg px-8 py-6">
                <Key className="w-5 h-5 mr-2" />
                Get API Key
              </Button>
            </Link>
            <Link href="/documentation">
              <Button variant="outline" size="lg" className="text-lg px-8 py-6">
                <BookOpen className="w-5 h-5 mr-2" />
                Documentation
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Authentication */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Authentication
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              All API requests require authentication using an API key
            </p>
          </div>

          <Card className="max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-6 w-6 mr-2" />
                API Key Authentication
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold mb-2">Base URL</h4>
                  <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <code>https://api.pdftools.pro/v1</code>
                  </pre>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Authorization Header</h4>
                  <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                    <code>Authorization: Bearer YOUR_API_KEY</code>
                  </pre>
                </div>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <p className="text-blue-800 dark:text-blue-200">
                    <strong>Note:</strong> Get your API key from your dashboard after signing up. Keep it secure and never expose it in client-side code.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Endpoints */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              API Endpoints
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Available endpoints for PDF processing and conversion
            </p>
          </div>

          <div className="space-y-6">
            {endpoints.map((endpoint, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center">
                      <Badge variant={endpoint.method === 'POST' ? 'default' : 'secondary'} className="mr-3">
                        {endpoint.method}
                      </Badge>
                      <code className="text-lg">{endpoint.path}</code>
                    </CardTitle>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300">{endpoint.description}</p>
                </CardHeader>
                <CardContent>
                  <div>
                    <h4 className="font-semibold mb-3">Parameters</h4>
                    <div className="space-y-2">
                      {endpoint.parameters.map((param, paramIndex) => (
                        <div key={paramIndex} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <code className="font-mono text-sm">{param.name}</code>
                            <Badge variant="outline">{param.type}</Badge>
                            {param.required && <Badge variant="destructive">Required</Badge>}
                          </div>
                          <span className="text-sm text-gray-600 dark:text-gray-300">{param.description}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Rate Limits */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Rate Limits
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              API usage limits by subscription tier
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {rateLimits.map((limit, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                    <Clock className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle>{limit.tier}</CardTitle>
                  <p className="text-2xl font-bold text-primary">{limit.requests}</p>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-300">{limit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Error Codes */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Error Codes
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Common HTTP status codes and their meanings
            </p>
          </div>

          <Card className="max-w-4xl mx-auto">
            <CardContent className="p-0">
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {errorCodes.map((error, index) => (
                  <div key={index} className="p-6 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Badge variant="destructive" className="text-lg px-3 py-1">
                        {error.code}
                      </Badge>
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white">{error.name}</h4>
                        <p className="text-gray-600 dark:text-gray-300">{error.description}</p>
                      </div>
                    </div>
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Example Response */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Example Response
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Successful API response format
            </p>
          </div>

          <Card className="max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle>Success Response</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-900 text-green-400 p-6 rounded-lg overflow-x-auto">
                <code>{`{
  "success": true,
  "downloadUrl": "https://api.pdftools.pro/download/abc123",
  "filename": "document.docx",
  "fileSize": 1024000,
  "processingTime": "2.3s",
  "expiresAt": "2024-01-01T12:00:00Z"
}`}</code>
              </pre>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* SDKs */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Official SDKs
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Use our official SDKs for faster integration
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
            {['JavaScript', 'Python', 'PHP', 'Java', 'C#'].map((sdk, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Code className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-semibold">{sdk}</h3>
                  <Button variant="outline" size="sm" className="mt-3">
                    Download
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-foreground text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-1">
              <div className="flex items-center mb-4">
                <span className="text-2xl font-bold">PDFTools Pro</span>
              </div>
              <p className="text-gray-300 mb-4">
                Professional PDF toolkit with custom checkout page generation for businesses and creators.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/tools"><span className="hover:text-white cursor-pointer">PDF Tools</span></Link></li>
                <li><Link href="/documentation"><span className="hover:text-white cursor-pointer">Documentation</span></Link></li>
                <li><Link href="/api-reference"><span className="hover:text-white cursor-pointer">API Reference</span></Link></li>
                <li><Link href="/status"><span className="hover:text-white cursor-pointer">Status Page</span></Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/documentation"><span className="hover:text-white cursor-pointer">Documentation</span></Link></li>
                <li><Link href="/api-reference"><span className="hover:text-white cursor-pointer">API Reference</span></Link></li>
                <li><Link href="/contact"><span className="hover:text-white cursor-pointer">Contact Support</span></Link></li>
                <li><Link href="/status"><span className="hover:text-white cursor-pointer">Status Page</span></Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/about"><span className="hover:text-white cursor-pointer">About Us</span></Link></li>
                <li><Link href="/contact"><span className="hover:text-white cursor-pointer">Contact</span></Link></li>
                <li><Link href="/privacy"><span className="hover:text-white cursor-pointer">Privacy Policy</span></Link></li>
                <li><Link href="/terms"><span className="hover:text-white cursor-pointer">Terms of Service</span></Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-300 text-sm">
              © 2024 PDFTools Pro. All rights reserved.
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <span className="text-gray-300 text-sm">Secured by</span>
              <div className="flex items-center space-x-2">
                <Shield className="text-green-500 h-4 w-4" />
                <span className="text-sm">256-bit SSL</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
