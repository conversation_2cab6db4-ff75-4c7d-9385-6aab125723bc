import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useUser } from "@/hooks/use-user";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from "recharts";
import { Users, CreditCard, FileText, Settings, Mail, Webhook, Shield, TrendingUp, Globe, Bell, ShoppingCart, Plus, Truck } from "lucide-react";
import SmtpManager from "./SmtpManager";
import PageManager from "./admin/PageManager";
import InvoiceManager from "./admin/InvoiceManager";

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  isPremium: boolean;
  usageCount: number;
  usageLimit: number;
  createdAt: string;
}

interface Analytics {
  totalUsers: number;
  totalRevenue: number;
  totalPdfProcessed: number;
  recentOperations: any[];
  monthlyRevenue: { month: string; revenue: number }[];
  userGrowth: { month: string; users: number }[];
  operationStats: { operation: string; count: number }[];
}

interface SystemConfig {
  siteName: string;
  supportEmail: string;
  maxFileSize: number;
  freeUserLimit: number;
  premiumPrice: number;
  premiumMonthlyPrice?: number;
  maintenanceMode: boolean;
}

export default function EnhancedAdminDashboard() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { data: userData } = useUser();
  const user = userData?.user;
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [editForm, setEditForm] = useState({
    username: '',
    email: '',
    role: '',
    isPremium: false,
    usageLimit: 100
  });
  const [configForm, setConfigForm] = useState<SystemConfig>({
    siteName: '',
    supportEmail: '',
    maxFileSize: 50,
    freeUserLimit: 5,
    premiumPrice: 48,
    premiumMonthlyPrice: 5,
    maintenanceMode: false
  });
  const [paymentGateways, setPaymentGateways] = useState({
    stripe: { publicKey: '', secretKey: '', sandbox: false },
    paypal: { clientId: '', clientSecret: '', sandbox: false },
    paypalStandard: { businessEmail: '' }, // No sandbox for PayPal Standard
    paddle: { vendorId: '', apiKey: '', productId: '', sandbox: false },
    sumup: { clientId: '', clientSecret: '', sandbox: false },
    wise: { apiKey: '', sandbox: false },
    payoneer: { programId: '', username: '', password: '', sandbox: false },
    bankTransfer: { bankName: '', accountNumber: '', routingNumber: '', accountName: '' },
    cod: {
      enabled: true,
      deliveryFee: 0,
      maxAmount: 1000,
      deliveryDays: 3,
      availableAreas: 'All areas',
      instructions: 'Payment will be collected upon delivery'
    }
  });

  const [businessDetails, setBusinessDetails] = useState({
    companyName: 'PDF Zone Pro',
    address: '123 Business Street\nSuite 100\nBusiness City, BC 12345',
    phone: '+****************',
    email: '<EMAIL>',
    website: 'www.pdfzone.pro',
    taxId: '',
    logoUrl: '',
    notes: ''
  });

  // Fetch analytics data
  const { data: analytics, isLoading: analyticsLoading } = useQuery({
    queryKey: ["/api/admin/analytics"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/admin/analytics");
      return res.json() as Promise<Analytics>;
    }
  });

  // Fetch current user
  const { data: currentUser } = useQuery({
    queryKey: ["/api/auth/me"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/auth/me");
      return res.json() as Promise<{ user: User }>;
    }
  });

  // Fetch users
  const { data: users, isLoading: usersLoading } = useQuery({
    queryKey: ["/api/admin/users"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/admin/users");
      return res.json() as Promise<User[]>;
    }
  });

  // Fetch system configuration
  const { data: systemConfig, isLoading: configLoading } = useQuery({
    queryKey: ["/api/admin/config"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/admin/config");
      const config = await res.json() as SystemConfig;
      setConfigForm(config); // Initialize form with fetched data
      return config;
    }
  });

  // Fetch payment gateway settings
  const { data: paymentGatewaySettings, isLoading: gatewaysLoading } = useQuery({
    queryKey: ["/api/admin/payment-gateways"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/admin/payment-gateways");
      const gateways = await res.json();
      setPaymentGateways(gateways); // Initialize form with fetched data
      return gateways;
    }
  });

  // Fetch business details
  const { data: businessDetailsData, isLoading: businessLoading } = useQuery({
    queryKey: ["/api/admin/business-details"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/admin/business-details");
      const details = await res.json();
      setBusinessDetails(details); // Initialize form with fetched data
      return details;
    }
  });

  // Handle edit user dialog
  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setEditForm({
      username: user.username,
      email: user.email,
      role: user.role,
      isPremium: user.isPremium,
      usageLimit: user.usageLimit || 100
    });
    setIsEditDialogOpen(true);
  };

  const handleSaveUser = () => {
    if (!selectedUser) return;

    updateUserMutation.mutate({
      userId: selectedUser.id,
      updates: {
        username: editForm.username,
        email: editForm.email,
        role: editForm.role,
        isPremium: editForm.isPremium,
        usageLimit: editForm.usageLimit
      }
    });
    setIsEditDialogOpen(false);
    setSelectedUser(null);
  };

  // Handle delete user dialog
  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (!userToDelete) return;

    deleteUserMutation.mutate(userToDelete.id);
    setIsDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: async ({ userId, updates }: { userId: number; updates: Partial<User> }) => {
      const res = await apiRequest("PUT", `/api/admin/users/${userId}`, updates);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      toast({ title: "Success", description: "User updated successfully" });
    },
    onError: (error: any) => {
      console.error('User update error:', error);
      toast({ title: "Error", description: "Failed to update user", variant: "destructive" });
    }
  });

  // Delete user mutation
  const deleteUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const res = await apiRequest("DELETE", `/api/admin/users/${userId}`);
      return res.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      toast({
        title: "Success",
        description: `User "${data.deletedUser.username}" deleted successfully`
      });
    },
    onError: (error: any) => {
      console.error('User deletion error:', error);
      const errorMessage = error.message || "Failed to delete user";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  });

  // Update system config mutation
  const updateConfigMutation = useMutation({
    mutationFn: async (config: Partial<SystemConfig>) => {
      const res = await apiRequest("POST", "/api/admin/config", config);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/config"] });
      toast({ title: "Success", description: "Configuration updated successfully" });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to update configuration", variant: "destructive" });
    }
  });

  // Update payment gateways
  const updatePaymentGatewaysMutation = useMutation({
    mutationFn: async (gateways: any) => {
      const res = await fetch("/api/admin/payment-gateways", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(gateways),
        credentials: "include"
      });

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      return res.json();
    },
    onSuccess: () => {
      toast({ title: "Success", description: "Payment gateway settings updated successfully" });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/payment-gateways"] });
    },
    onError: (error) => {
      console.error("Payment gateway update error:", error);
      toast({ title: "Error", description: "Failed to update payment gateways", variant: "destructive" });
    }
  });

  // Update business details
  const updateBusinessDetailsMutation = useMutation({
    mutationFn: async (details: any) => {
      console.log("🔄 Updating business details:", details);

      try {
        const res = await apiRequest("PUT", "/api/admin/business-details", details);
        console.log("📡 Response status:", res.status);
        console.log("📡 Response headers:", res.headers);

        // Check if response is actually JSON
        const contentType = res.headers.get("content-type");
        console.log("📡 Content-Type:", contentType);

        if (!contentType || !contentType.includes("application/json")) {
          // If not JSON, get the text to see what we actually received
          const text = await res.text();
          console.error("❌ Non-JSON response received:", text);
          throw new Error(`Server returned non-JSON response: ${text.substring(0, 200)}...`);
        }

        const result = await res.json();
        console.log("✅ Business details update response:", result);
        return result;
      } catch (error) {
        console.error("❌ Request failed:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Business details update successful:", data);
      toast({ title: "Success", description: "Business details updated successfully" });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/business-details"] });
    },
    onError: (error: any) => {
      console.error("❌ Business details update error:", error);
      const errorMessage = error.message || "Failed to update business details";
      toast({ title: "Error", description: errorMessage, variant: "destructive" });
    }
  });

  const CHART_COLORS = ['#635BFF', '#00D4FF', '#32D583', '#FF6B6B', '#4ECDC4'];

  if (analyticsLoading || usersLoading || configLoading || gatewaysLoading || businessLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <div className="flex gap-2">
          <Badge variant="outline">
            <Shield className="w-4 h-4 mr-1" />
            Administrator
          </Badge>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className={`grid w-full ${user?.isPrimaryAdmin ? 'grid-cols-10' : 'grid-cols-9'}`}>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="pages">Pages</TabsTrigger>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
          <TabsTrigger value="business">Business</TabsTrigger>
          <TabsTrigger value="payments">Payment Gateways</TabsTrigger>
          <TabsTrigger value="email">SMTP Settings</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          {/* Only show Checkout Pages tab to primary admin */}
          {user?.isPrimaryAdmin && <TabsTrigger value="checkout">Checkout Pages</TabsTrigger>}
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics?.totalUsers || 0}</div>
                <p className="text-xs text-muted-foreground">+12% from last month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${analytics?.totalRevenue || 0}</div>
                <p className="text-xs text-muted-foreground">+8% from last month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">PDFs Processed</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics?.totalPdfProcessed || 0}</div>
                <p className="text-xs text-muted-foreground">+23% from last month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">+15.3%</div>
                <p className="text-xs text-muted-foreground">Monthly growth</p>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={analytics?.monthlyRevenue || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="revenue" stroke="#635BFF" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Operation Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={analytics?.operationStats || []}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      label
                    >
                      {analytics?.operationStats?.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>Manage user accounts, permissions, and usage limits</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users?.users?.length > 0 ? (
                    users.users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.username}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                            {user.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={user.isPremium ? 'default' : 'outline'}>
                            {user.isPremium ? 'Premium' : 'Free'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {user.usageCount}/{user.usageLimit}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditUser(user)}
                            >
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant={user.isPremium ? "destructive" : "default"}
                              onClick={() => updateUserMutation.mutate({
                                userId: user.id,
                                updates: { isPremium: !user.isPremium }
                              })}
                            >
                              {user.isPremium ? 'Revoke' : 'Grant'} Premium
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDeleteUser(user)}
                              disabled={
                                user.role === 'admin' ||
                                user.id === currentUser?.user?.id ||
                                deleteUserMutation.isPending
                              }
                              title={
                                user.role === 'admin'
                                  ? "Cannot delete admin users"
                                  : user.id === currentUser?.user?.id
                                    ? "Cannot delete your own account"
                                    : "Delete this user"
                              }
                            >
                              {deleteUserMutation.isPending ? 'Deleting...' : 'Delete'}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        No users found. User management features coming soon.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Edit User Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Edit User</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-username">Username</Label>
                  <Input
                    id="edit-username"
                    value={editForm.username}
                    onChange={(e) => setEditForm(prev => ({ ...prev, username: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-email">Email</Label>
                  <Input
                    id="edit-email"
                    type="email"
                    value={editForm.email}
                    onChange={(e) => setEditForm(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-role">Role</Label>
                  <Select
                    value={editForm.role}
                    onValueChange={(value) => setEditForm(prev => ({ ...prev, role: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">User</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-usage-limit">Usage Limit</Label>
                  <Input
                    id="edit-usage-limit"
                    type="number"
                    value={editForm.usageLimit}
                    onChange={(e) => setEditForm(prev => ({ ...prev, usageLimit: parseInt(e.target.value) || 100 }))}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-premium"
                    checked={editForm.isPremium}
                    onCheckedChange={(checked) => setEditForm(prev => ({ ...prev, isPremium: checked }))}
                  />
                  <Label htmlFor="edit-premium">Premium Status</Label>
                </div>
                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsEditDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSaveUser}
                    disabled={updateUserMutation.isPending}
                  >
                    {updateUserMutation.isPending ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {/* Delete User Confirmation Dialog */}
          <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Delete User</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Are you sure you want to delete user <strong>{userToDelete?.username}</strong>?
                  This action cannot be undone.
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-sm text-yellow-800">
                    <strong>What will happen:</strong>
                  </p>
                  <ul className="text-sm text-yellow-700 mt-1 space-y-1">
                    <li>• User account will be permanently deleted</li>
                    <li>• Payment records will be preserved for audit purposes</li>
                    <li>• Invoices will be maintained for business records</li>
                    <li>• User's personal data will be removed</li>
                  </ul>
                </div>
                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsDeleteDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleConfirmDelete}
                    disabled={deleteUserMutation.isPending}
                  >
                    {deleteUserMutation.isPending ? 'Deleting...' : 'Delete User'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </TabsContent>

        {/* Pages Tab */}
        <TabsContent value="pages" className="space-y-6">
          <PageManager />
        </TabsContent>

        {/* Invoices Tab */}
        <TabsContent value="invoices" className="space-y-6">
          <InvoiceManager />
        </TabsContent>

        {/* Business Details Tab */}
        <TabsContent value="business" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Business Details</CardTitle>
              <CardDescription>
                Manage your company information for invoices and customer communications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="company-name">Company Name *</Label>
                    <Input
                      id="company-name"
                      placeholder="Your Company Name"
                      value={businessDetails.companyName}
                      onChange={(e) => setBusinessDetails({
                        ...businessDetails,
                        companyName: e.target.value
                      })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company-address">Address *</Label>
                    <textarea
                      id="company-address"
                      className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="123 Business Street&#10;Suite 100&#10;Business City, BC 12345"
                      value={businessDetails.address}
                      onChange={(e) => setBusinessDetails({
                        ...businessDetails,
                        address: e.target.value
                      })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company-phone">Phone Number *</Label>
                    <Input
                      id="company-phone"
                      placeholder="+****************"
                      value={businessDetails.phone}
                      onChange={(e) => setBusinessDetails({
                        ...businessDetails,
                        phone: e.target.value
                      })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company-email">Email Address *</Label>
                    <Input
                      id="company-email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={businessDetails.email}
                      onChange={(e) => setBusinessDetails({
                        ...businessDetails,
                        email: e.target.value
                      })}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="company-website">Website *</Label>
                    <Input
                      id="company-website"
                      placeholder="www.yourcompany.com"
                      value={businessDetails.website}
                      onChange={(e) => setBusinessDetails({
                        ...businessDetails,
                        website: e.target.value
                      })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company-tax-id">Tax ID / Registration Number</Label>
                    <Input
                      id="company-tax-id"
                      placeholder="Tax ID or Business Registration Number"
                      value={businessDetails.taxId}
                      onChange={(e) => setBusinessDetails({
                        ...businessDetails,
                        taxId: e.target.value
                      })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company-logo">Logo URL</Label>
                    <Input
                      id="company-logo"
                      placeholder="https://yourcompany.com/logo.png"
                      value={businessDetails.logoUrl}
                      onChange={(e) => setBusinessDetails({
                        ...businessDetails,
                        logoUrl: e.target.value
                      })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company-notes">Additional Notes</Label>
                    <textarea
                      id="company-notes"
                      className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Additional business information or notes..."
                      value={businessDetails.notes}
                      onChange={(e) => setBusinessDetails({
                        ...businessDetails,
                        notes: e.target.value
                      })}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end pt-4">
                <Button
                  onClick={() => updateBusinessDetailsMutation.mutate(businessDetails)}
                  disabled={updateBusinessDetailsMutation.isPending}
                >
                  {updateBusinessDetailsMutation.isPending ? 'Saving...' : 'Save Business Details'}
                </Button>
              </div>

              <div className="mt-6 p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">How this information is used:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Displayed on generated invoices and receipts</li>
                  <li>• Used in customer email communications</li>
                  <li>• Shown on checkout pages for transparency</li>
                  <li>• Required for professional business documentation</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payment Gateways Tab */}
        <TabsContent value="payments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payment Gateway Configuration</CardTitle>
              <CardDescription>Configure API credentials for all payment gateways</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Stripe */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Stripe
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="stripe-sandbox"
                      checked={paymentGateways.stripe.sandbox}
                      onCheckedChange={(checked) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          stripe: { ...paymentGateways.stripe, sandbox: checked }
                        })
                      }
                    />
                    <Label htmlFor="stripe-sandbox">Sandbox Mode</Label>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="stripe-public">Publishable Key</Label>
                      <Input
                        id="stripe-public"
                        placeholder={paymentGateways.stripe.sandbox ? "pk_test_..." : "pk_live_..."}
                        type="password"
                        value={paymentGateways.stripe.publicKey}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            stripe: { ...paymentGateways.stripe, publicKey: e.target.value }
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="stripe-secret">Secret Key</Label>
                      <Input
                        id="stripe-secret"
                        placeholder={paymentGateways.stripe.sandbox ? "sk_test_..." : "sk_live_..."}
                        type="password"
                        value={paymentGateways.stripe.secretKey}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            stripe: { ...paymentGateways.stripe, secretKey: e.target.value }
                          })
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* PayPal */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  PayPal
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="paypal-sandbox"
                      checked={paymentGateways.paypal.sandbox}
                      onCheckedChange={(checked) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          paypal: { ...paymentGateways.paypal, sandbox: checked }
                        })
                      }
                    />
                    <Label htmlFor="paypal-sandbox">Sandbox Mode</Label>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="paypal-client-id">Client ID</Label>
                      <Input
                        id="paypal-client-id"
                        placeholder={paymentGateways.paypal.sandbox ? "sandbox-client-id" : "live-client-id"}
                        value={paymentGateways.paypal.clientId}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            paypal: { ...paymentGateways.paypal, clientId: e.target.value }
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="paypal-secret">Client Secret</Label>
                      <Input
                        id="paypal-secret"
                        placeholder={paymentGateways.paypal.sandbox ? "sandbox-client-secret" : "live-client-secret"}
                        type="password"
                        value={paymentGateways.paypal.clientSecret}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            paypal: { ...paymentGateways.paypal, clientSecret: e.target.value }
                          })
                        }
                      />
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {paymentGateways.paypal.sandbox
                      ? "Using PayPal sandbox for testing. Switch off for live payments."
                      : "Using live PayPal environment for real payments."
                    }
                  </p>
                </div>
              </div>

              {/* PayPal Standard */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  PayPal Standard
                </h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="paypal-standard-email">Business Email</Label>
                    <Input
                      id="paypal-standard-email"
                      placeholder="<EMAIL>"
                      type="email"
                      value={paymentGateways.paypalStandard.businessEmail}
                      onChange={(e) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          paypalStandard: { ...paymentGateways.paypalStandard, businessEmail: e.target.value }
                        })
                      }
                    />
                    <p className="text-sm text-muted-foreground">
                      Your PayPal business account email address for receiving payments.
                      PayPal Standard uses your live business account directly.
                    </p>
                  </div>
                </div>
              </div>

              {/* Paddle */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Paddle
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="paddle-sandbox"
                      checked={paymentGateways.paddle.sandbox}
                      onCheckedChange={(checked) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          paddle: { ...paymentGateways.paddle, sandbox: checked }
                        })
                      }
                    />
                    <Label htmlFor="paddle-sandbox">Sandbox Mode</Label>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="paddle-vendor">Vendor ID</Label>
                      <Input
                        id="paddle-vendor"
                        placeholder="Vendor ID..."
                        value={paymentGateways.paddle.vendorId}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            paddle: { ...paymentGateways.paddle, vendorId: e.target.value }
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="paddle-api">API Key</Label>
                      <Input
                        id="paddle-api"
                        placeholder="API Key..."
                        type="password"
                        value={paymentGateways.paddle.apiKey}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            paddle: { ...paymentGateways.paddle, apiKey: e.target.value }
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="paddle-product">Product ID</Label>
                      <Input
                        id="paddle-product"
                        placeholder="Product ID..."
                        value={paymentGateways.paddle.productId}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            paddle: { ...paymentGateways.paddle, productId: e.target.value }
                          })
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* SumUp */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  SumUp
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="sumup-sandbox"
                      checked={paymentGateways.sumup.sandbox}
                      onCheckedChange={(checked) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          sumup: { ...paymentGateways.sumup, sandbox: checked }
                        })
                      }
                    />
                    <Label htmlFor="sumup-sandbox">Sandbox Mode</Label>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="sumup-client-id">Client ID</Label>
                      <Input
                        id="sumup-client-id"
                        placeholder="Client ID..."
                        value={paymentGateways.sumup.clientId}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            sumup: { ...paymentGateways.sumup, clientId: e.target.value }
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="sumup-secret">Client Secret</Label>
                      <Input
                        id="sumup-secret"
                        placeholder="Client Secret..."
                        type="password"
                        value={paymentGateways.sumup.clientSecret}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            sumup: { ...paymentGateways.sumup, clientSecret: e.target.value }
                          })
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Wise */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Wise (TransferWise)
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="wise-sandbox"
                      checked={paymentGateways.wise.sandbox}
                      onCheckedChange={(checked) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          wise: { ...paymentGateways.wise, sandbox: checked }
                        })
                      }
                    />
                    <Label htmlFor="wise-sandbox">Sandbox Mode</Label>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="wise-api">API Key</Label>
                    <Input
                      id="wise-api"
                      placeholder="API Key..."
                      type="password"
                      value={paymentGateways.wise.apiKey}
                      onChange={(e) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          wise: { ...paymentGateways.wise, apiKey: e.target.value }
                        })
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Payoneer */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Payoneer
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="payoneer-sandbox"
                      checked={paymentGateways.payoneer.sandbox}
                      onCheckedChange={(checked) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          payoneer: { ...paymentGateways.payoneer, sandbox: checked }
                        })
                      }
                    />
                    <Label htmlFor="payoneer-sandbox">Sandbox Mode</Label>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="payoneer-program">Program ID</Label>
                      <Input
                        id="payoneer-program"
                        placeholder="Program ID..."
                        value={paymentGateways.payoneer.programId}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            payoneer: { ...paymentGateways.payoneer, programId: e.target.value }
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="payoneer-username">Username</Label>
                      <Input
                        id="payoneer-username"
                        placeholder="Username..."
                        value={paymentGateways.payoneer.username}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            payoneer: { ...paymentGateways.payoneer, username: e.target.value }
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="payoneer-password">Password</Label>
                      <Input
                        id="payoneer-password"
                        placeholder="Password..."
                        type="password"
                        value={paymentGateways.payoneer.password}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            payoneer: { ...paymentGateways.payoneer, password: e.target.value }
                          })
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Bank Transfer */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Bank Transfer
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="bank-name">Bank Name</Label>
                    <Input
                      id="bank-name"
                      placeholder="Your Bank Name..."
                      value={paymentGateways.bankTransfer.bankName}
                      onChange={(e) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          bankTransfer: { ...paymentGateways.bankTransfer, bankName: e.target.value }
                        })
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bank-account">Account Number</Label>
                    <Input
                      id="bank-account"
                      placeholder="Account Number..."
                      value={paymentGateways.bankTransfer.accountNumber}
                      onChange={(e) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          bankTransfer: { ...paymentGateways.bankTransfer, accountNumber: e.target.value }
                        })
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bank-routing">Routing Number</Label>
                    <Input
                      id="bank-routing"
                      placeholder="Routing Number..."
                      value={paymentGateways.bankTransfer.routingNumber}
                      onChange={(e) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          bankTransfer: { ...paymentGateways.bankTransfer, routingNumber: e.target.value }
                        })
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bank-account-name">Account Name</Label>
                    <Input
                      id="bank-account-name"
                      placeholder="Account Holder Name..."
                      value={paymentGateways.bankTransfer.accountName}
                      onChange={(e) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          bankTransfer: { ...paymentGateways.bankTransfer, accountName: e.target.value }
                        })
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Cash on Delivery */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <Truck className="w-5 h-5" />
                  Cash on Delivery (COD)
                </h3>
                {gatewaysLoading ? (
                  <div className="flex justify-center py-4">
                    <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full" />
                  </div>
                ) : (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="cod-enabled"
                      checked={paymentGateways.cod?.enabled || false}
                      onCheckedChange={(checked) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          cod: { ...(paymentGateways.cod || {}), enabled: checked }
                        })
                      }
                    />
                    <Label htmlFor="cod-enabled">Enable Cash on Delivery</Label>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="cod-delivery-fee">Delivery Fee ($)</Label>
                      <Input
                        id="cod-delivery-fee"
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                        value={paymentGateways.cod?.deliveryFee || 0}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            cod: { ...(paymentGateways.cod || {}), deliveryFee: parseFloat(e.target.value) || 0 }
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cod-max-amount">Maximum Order Amount ($)</Label>
                      <Input
                        id="cod-max-amount"
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="1000.00"
                        value={paymentGateways.cod?.maxAmount || 1000}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            cod: { ...(paymentGateways.cod || {}), maxAmount: parseFloat(e.target.value) || 1000 }
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cod-delivery-days">Delivery Days</Label>
                      <Input
                        id="cod-delivery-days"
                        type="number"
                        min="1"
                        max="30"
                        placeholder="3"
                        value={paymentGateways.cod?.deliveryDays || 3}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            cod: { ...(paymentGateways.cod || {}), deliveryDays: parseInt(e.target.value) || 3 }
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cod-available-areas">Available Areas</Label>
                      <Input
                        id="cod-available-areas"
                        placeholder="All areas"
                        value={paymentGateways.cod?.availableAreas || 'All areas'}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            cod: { ...(paymentGateways.cod || {}), availableAreas: e.target.value }
                          })
                        }
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="cod-instructions">Customer Instructions</Label>
                    <textarea
                      id="cod-instructions"
                      className="w-full p-2 border rounded-md"
                      rows={3}
                      placeholder="Payment will be collected upon delivery"
                      value={paymentGateways.cod?.instructions || 'Payment will be collected upon delivery'}
                      onChange={(e) =>
                        setPaymentGateways({
                          ...paymentGateways,
                          cod: { ...(paymentGateways.cod || {}), instructions: e.target.value }
                        })
                      }
                    />
                  </div>

                  <div className="bg-blue-50 p-3 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>COD Testing:</strong> This payment method allows customers to place orders without immediate payment.
                      Perfect for testing the complete checkout workflow, customer data capture, and invoice generation.
                    </p>
                  </div>
                </div>
                )}
              </div>

              <Button
                className="w-full"
                onClick={() => updatePaymentGatewaysMutation.mutate(paymentGateways)}
                disabled={updatePaymentGatewaysMutation.isPending}
              >
                {updatePaymentGatewaysMutation.isPending ? (
                  <>
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <CreditCard className="w-4 h-4 mr-2" />
                    Save Payment Gateway Settings
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Webhooks Tab */}
        <TabsContent value="webhooks" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Webhook Configuration</CardTitle>
              <CardDescription>Configure webhooks for payment notifications and system events</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  Payment Webhooks
                </h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="webhook-url">Webhook URL</Label>
                    <Input
                      id="webhook-url"
                      placeholder="https://your-domain.com/api/webhooks/payment"
                      value=""
                    />
                    <p className="text-sm text-muted-foreground">
                      URL where payment notifications will be sent
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="webhook-secret">Webhook Secret</Label>
                    <Input
                      id="webhook-secret"
                      placeholder="Your webhook secret key"
                      type="password"
                      value=""
                    />
                    <p className="text-sm text-muted-foreground">
                      Secret key for webhook signature verification
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="webhook-enabled" />
                    <Label htmlFor="webhook-enabled">Enable Payment Webhooks</Label>
                  </div>
                </div>
              </div>

              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <Bell className="w-5 h-5" />
                  System Event Webhooks
                </h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="system-webhook-url">System Webhook URL</Label>
                    <Input
                      id="system-webhook-url"
                      placeholder="https://your-domain.com/api/webhooks/system"
                      value=""
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Switch id="webhook-user-signup" />
                      <Label htmlFor="webhook-user-signup">User Signup</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="webhook-pdf-processed" />
                      <Label htmlFor="webhook-pdf-processed">PDF Processed</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="webhook-payment-success" />
                      <Label htmlFor="webhook-payment-success">Payment Success</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="webhook-payment-failed" />
                      <Label htmlFor="webhook-payment-failed">Payment Failed</Label>
                    </div>
                  </div>
                </div>
              </div>

              <Button className="w-full">
                <Globe className="w-4 h-4 mr-2" />
                Save Webhook Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Checkout Pages Tab - Only for Primary Admin */}
        {user?.isPrimaryAdmin && (
          <TabsContent value="checkout" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Checkout Pages Management</CardTitle>
                <CardDescription>Manage and monitor your checkout pages</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <ShoppingCart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Checkout Pages</h3>
                  <p className="text-muted-foreground mb-6">
                    View and manage all your checkout pages from here.
                  </p>
                  <Button asChild>
                    <a href="/checkout-builder">
                      <Plus className="w-4 h-4 mr-2" />
                      Create New Checkout Page
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* SMTP Settings Tab */}
        <TabsContent value="email" className="space-y-6">
          <SmtpManager />
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>System Configuration</CardTitle>
              <CardDescription>Configure global system settings</CardDescription>
            </CardHeader>
            <CardContent>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  updateConfigMutation.mutate(configForm);
                }}
                className="space-y-4"
              >
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="siteName">Site Name</Label>
                      <Input
                        id="siteName"
                        value={configForm.siteName || ''}
                        onChange={(e) => setConfigForm({ ...configForm, siteName: e.target.value })}
                        placeholder="PDF Zone Pro"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="supportEmail">Support Email</Label>
                      <Input
                        id="supportEmail"
                        type="email"
                        value={configForm.supportEmail || ''}
                        onChange={(e) => setConfigForm({ ...configForm, supportEmail: e.target.value })}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maxFileSize">Max File Size (MB)</Label>
                      <Input
                        id="maxFileSize"
                        type="number"
                        value={configForm.maxFileSize || 50}
                        onChange={(e) => setConfigForm({ ...configForm, maxFileSize: parseInt(e.target.value) || 50 })}
                        placeholder="50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="freeUserLimit">Free User Limit</Label>
                      <Input
                        id="freeUserLimit"
                        type="number"
                        value={configForm.freeUserLimit || 5}
                        onChange={(e) => setConfigForm({ ...configForm, freeUserLimit: parseInt(e.target.value) || 5 })}
                        placeholder="5"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="premiumPrice">Premium Yearly Price ($)</Label>
                      <Input
                        id="premiumPrice"
                        type="number"
                        value={configForm.premiumPrice || 48}
                        onChange={(e) => setConfigForm({ ...configForm, premiumPrice: parseInt(e.target.value) || 48 })}
                        placeholder="48"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="premiumMonthlyPrice">Premium Monthly Price ($)</Label>
                      <Input
                        id="premiumMonthlyPrice"
                        type="number"
                        value={configForm.premiumMonthlyPrice || 5}
                        onChange={(e) => setConfigForm({ ...configForm, premiumMonthlyPrice: parseInt(e.target.value) || 5 })}
                        placeholder="5"
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="maintenance"
                      checked={configForm.maintenanceMode || false}
                      onCheckedChange={(checked) => setConfigForm({ ...configForm, maintenanceMode: checked })}
                    />
                    <Label htmlFor="maintenance">Maintenance Mode</Label>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button
                      type="submit"
                      disabled={updateConfigMutation.isPending}
                      className="flex-1"
                    >
                      {updateConfigMutation.isPending ? (
                        <>
                          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Settings className="w-4 h-4 mr-2" />
                          Save Configuration
                        </>
                      )}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setConfigForm(systemConfig || {
                        siteName: '',
                        supportEmail: '',
                        maxFileSize: 50,
                        freeUserLimit: 5,
                        premiumPrice: 48,
                        premiumMonthlyPrice: 5,
                        maintenanceMode: false
                      })}
                    >
                      Reset
                    </Button>
                  </div>
                </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}