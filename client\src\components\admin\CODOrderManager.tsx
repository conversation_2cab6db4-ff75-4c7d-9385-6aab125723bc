import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Truck, Package, DollarSign, Calendar, User, MapPin, Phone, Mail, Eye, Edit, CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface CODOrder {
  id: number;
  gatewayTransactionId: string;
  amount: string;
  currency: string;
  status: string;
  customerData: any;
  invoice: any;
  createdAt: string;
}

export default function CODOrderManager() {
  const [orders, setOrders] = useState<CODOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<CODOrder | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchCODOrders();
  }, []);

  const fetchCODOrders = async () => {
    try {
      const response = await fetch("/api/cod/orders");
      if (response.ok) {
        const data = await response.json();
        setOrders(data);
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch COD orders",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch COD orders",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (orderId: string, status: string) => {
    try {
      const response = await fetch(`/api/cod/orders/${orderId}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Order status updated successfully",
        });
        fetchCODOrders();
      } else {
        toast({
          title: "Error",
          description: "Failed to update order status",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update order status",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending_delivery: { color: "bg-yellow-500", icon: Clock, label: "Pending Delivery" },
      out_for_delivery: { color: "bg-blue-500", icon: Truck, label: "Out for Delivery" },
      delivered: { color: "bg-green-500", icon: CheckCircle, label: "Delivered" },
      completed: { color: "bg-green-600", icon: CheckCircle, label: "Completed" },
      cancelled: { color: "bg-red-500", icon: XCircle, label: "Cancelled" },
      failed: { color: "bg-red-600", icon: AlertCircle, label: "Failed" }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending_delivery;
    const Icon = config.icon;
    
    return (
      <Badge className={`${config.color} text-white flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: string, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(parseFloat(amount));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleViewOrder = (order: CODOrder) => {
    setSelectedOrder(order);
    setIsViewDialogOpen(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Cash on Delivery Orders</h2>
          <p className="text-gray-600">Manage COD orders and delivery status</p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Package className="w-5 h-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Total Orders</p>
                <p className="text-xl font-bold">{orders.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Truck className="w-5 h-5 text-orange-500" />
              <div>
                <p className="text-sm text-gray-600">Pending Delivery</p>
                <p className="text-xl font-bold">{orders.filter(order => order.status === 'pending_delivery').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-xl font-bold">{orders.filter(order => order.status === 'completed' || order.status === 'delivered').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="w-5 h-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">Total Value</p>
                <p className="text-xl font-bold">
                  {formatCurrency(
                    orders.reduce((sum, order) => sum + parseFloat(order.amount), 0).toString(),
                    orders[0]?.currency || 'USD'
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>All COD Orders</CardTitle>
        </CardHeader>
        <CardContent>
          {orders.length === 0 ? (
            <div className="text-center py-8">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No COD orders found</p>
              <p className="text-sm text-gray-500">COD orders will appear here when customers place them</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">{order.gatewayTransactionId}</TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{order.customerData?.name || 'Unknown Customer'}</p>
                        <p className="text-sm text-gray-600">{order.customerData?.email || 'No email'}</p>
                      </div>
                    </TableCell>
                    <TableCell>{formatCurrency(order.amount, order.currency)}</TableCell>
                    <TableCell>{getStatusBadge(order.status)}</TableCell>
                    <TableCell>{formatDate(order.createdAt)}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewOrder(order)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Select
                          value={order.status}
                          onValueChange={(value) => updateOrderStatus(order.gatewayTransactionId, value)}
                        >
                          <SelectTrigger className="w-40">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pending_delivery">Pending Delivery</SelectItem>
                            <SelectItem value="out_for_delivery">Out for Delivery</SelectItem>
                            <SelectItem value="delivered">Delivered</SelectItem>
                            <SelectItem value="completed">Completed</SelectItem>
                            <SelectItem value="cancelled">Cancelled</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* View Order Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>COD Order Details</DialogTitle>
          </DialogHeader>
          {selectedOrder && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-2 flex items-center gap-2">
                    <User className="w-4 h-4" />
                    Customer Information
                  </h3>
                  <div className="space-y-1 text-sm">
                    <p><strong>Name:</strong> {selectedOrder.customerData?.name || 'Not provided'}</p>
                    <p><strong>Email:</strong> {selectedOrder.customerData?.email || 'Not provided'}</p>
                    <p><strong>Phone:</strong> {selectedOrder.customerData?.phone || 'Not provided'}</p>
                    <p><strong>Address:</strong> {selectedOrder.customerData?.address || 'Not provided'}</p>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold mb-2 flex items-center gap-2">
                    <Package className="w-4 h-4" />
                    Order Information
                  </h3>
                  <div className="space-y-1 text-sm">
                    <p><strong>Order ID:</strong> {selectedOrder.gatewayTransactionId}</p>
                    <p><strong>Status:</strong> {getStatusBadge(selectedOrder.status)}</p>
                    <p><strong>Amount:</strong> {formatCurrency(selectedOrder.amount, selectedOrder.currency)}</p>
                    <p><strong>Date:</strong> {formatDate(selectedOrder.createdAt)}</p>
                  </div>
                </div>
              </div>
              
              {selectedOrder.invoice && (
                <div>
                  <h3 className="font-semibold mb-2">Invoice Information</h3>
                  <div className="border rounded-lg p-4">
                    <p><strong>Invoice #:</strong> {selectedOrder.invoice.invoiceNumber}</p>
                    <p><strong>Product:</strong> {selectedOrder.invoice.productName}</p>
                    <p><strong>Status:</strong> {selectedOrder.invoice.status}</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
