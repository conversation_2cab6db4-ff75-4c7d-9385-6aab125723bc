import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/hooks/use-user";
import { Link } from "wouter";
import PDFProcessor from "./PDFProcessor";
import {
  ImageIcon,
  FileText,
  Signature,
  FormInput,
  Zap,
  Crown,
  Download,
  Upload
} from "lucide-react";

export default function AdvancedPDFTools() {
  const { toast } = useToast();
  const { user } = useUser();
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<any>(null);

  const isPremium = user?.isPremium || user?.role === 'admin' || user?.username === 'admin' || false;

  const processAdvancedFile = async (endpoint: string, formData: FormData) => {
    setIsProcessing(true);
    setResult(null);

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Processing failed');
      }

      if (response.headers.get('content-type')?.includes('application/pdf')) {
        // Handle PDF download
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = response.headers.get('content-disposition')?.split('filename=')[1] || 'processed.pdf';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        toast({
          title: "Success",
          description: "PDF processed and downloaded successfully!"
        });
      } else {
        const result = await response.json();
        setResult(result);

        toast({
          title: "Success",
          description: result.message || "Processing completed successfully!"
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Processing failed",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const advancedTools = [
    {
      id: 'pdf-to-images',
      title: 'PDF to Images',
      description: 'Convert PDF pages to image files (PNG, JPG)',
      icon: ImageIcon,
      endpoint: '/api/pdf/convert-to-images',
      isPremium: false,
      fields: [
        { name: 'format', type: 'select', label: 'Image Format', options: ['png', 'jpg', 'jpeg'], default: 'png' },
        { name: 'dpi', type: 'number', label: 'DPI Quality', default: '150' },
        { name: 'pages', type: 'text', label: 'Pages (e.g., 1-3 or 1,3,5)', placeholder: 'All pages' }
      ]
    },
    {
      id: 'images-to-pdf',
      title: 'Images to PDF',
      description: 'Combine multiple images into a single PDF',
      icon: FileText,
      endpoint: '/api/pdf/images-to-pdf',
      isPremium: false,
      acceptMultiple: true,
      accept: 'image/*',
      fields: [
        { name: 'pageSize', type: 'select', label: 'Page Size', options: ['A4', 'Letter', 'Legal'], default: 'A4' },
        { name: 'orientation', type: 'select', label: 'Orientation', options: ['portrait', 'landscape'], default: 'portrait' }
      ]
    },
    {
      id: 'extract-text',
      title: 'Extract Text (OCR)',
      description: 'Extract text from PDF using OCR technology',
      icon: FileText,
      endpoint: '/api/pdf/extract-text',
      isPremium: false,
      fields: []
    },
    {
      id: 'fill-form',
      title: 'Fill PDF Forms',
      description: 'Automatically fill PDF form fields',
      icon: FormInput,
      endpoint: '/api/pdf/fill-form',
      isPremium: false,
      fields: [
        { name: 'formData', type: 'textarea', label: 'Form Data (JSON)', placeholder: '{"fieldName": "value", "anotherField": "value"}' }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Advanced PDF Tools</h1>
          <p className="text-muted-foreground">Professional PDF processing with OCR, conversion, and form filling</p>
        </div>
        {!isPremium && (
          <Link href="/subscribe">
            <Badge variant="outline" className="text-primary border-primary hover:bg-primary/10 cursor-pointer transition-colors">
              <Crown className="w-4 h-4 mr-1" />
              Upgrade for Full Access
            </Badge>
          </Link>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {advancedTools.map((tool) => (
          <Card key={tool.id} className={`relative ${tool.isPremium && !isPremium ? 'opacity-75' : ''}`}>
            {tool.isPremium && !isPremium && (
              <div className="absolute top-2 right-2">
                <Badge variant="secondary">
                  <Crown className="w-3 h-3 mr-1" />
                  Premium
                </Badge>
              </div>
            )}

            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <tool.icon className="w-5 h-5" />
                {tool.title}
              </CardTitle>
              <CardDescription>{tool.description}</CardDescription>
            </CardHeader>

            <CardContent>
              <form onSubmit={async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                await processAdvancedFile(tool.endpoint, formData);
              }}>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor={`${tool.id}-file`}>
                      {tool.acceptMultiple ? 'Select Images' : 'Select PDF File'}
                    </Label>
                    <Input
                      id={`${tool.id}-file`}
                      name={tool.acceptMultiple ? 'images' : 'file'}
                      type="file"
                      accept={tool.accept || '.pdf'}
                      multiple={tool.acceptMultiple}
                      required
                      className="mt-2"
                    />
                  </div>

                  {tool.fields.map((field) => (
                    <div key={field.name}>
                      <Label htmlFor={`${tool.id}-${field.name}`}>{field.label}</Label>
                      {field.type === 'select' ? (
                        <Select name={field.name} defaultValue={field.default}>
                          <SelectTrigger className="mt-2">
                            <SelectValue placeholder={`Select ${field.label}`} />
                          </SelectTrigger>
                          <SelectContent>
                            {field.options?.map((option) => (
                              <SelectItem key={option} value={option}>
                                {option.charAt(0).toUpperCase() + option.slice(1)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : field.type === 'textarea' ? (
                        <Textarea
                          id={`${tool.id}-${field.name}`}
                          name={field.name}
                          placeholder={field.placeholder}
                          className="mt-2"
                          rows={4}
                        />
                      ) : (
                        <Input
                          id={`${tool.id}-${field.name}`}
                          name={field.name}
                          type={field.type}
                          placeholder={field.placeholder}
                          defaultValue={field.default}
                          className="mt-2"
                        />
                      )}
                    </div>
                  ))}

                  <Button
                    type="submit"
                    disabled={isProcessing || (tool.isPremium && !isPremium)}
                    className="w-full"
                  >
                    {isProcessing ? (
                      <>
                        <Zap className="w-4 h-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Upload className="w-4 h-4 mr-2" />
                        {tool.title}
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        ))}
      </div>

      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="w-5 h-5" />
              Processing Result
            </CardTitle>
          </CardHeader>
          <CardContent>
            {result.text ? (
              <div className="space-y-4">
                <Label>Extracted Text:</Label>
                <Textarea
                  value={result.text}
                  readOnly
                  rows={10}
                  className="font-mono text-sm"
                />
                <Button onClick={() => navigator.clipboard.writeText(result.text)}>
                  Copy Text
                </Button>
              </div>
            ) : result.images ? (
              <div className="space-y-4">
                <Label>Converted Images:</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {result.images.map((imageUrl: string, index: number) => (
                    <div key={index} className="text-center">
                      <div className="bg-muted rounded-lg p-4 mb-2">
                        <ImageIcon className="w-12 h-12 mx-auto text-muted-foreground" />
                      </div>
                      <Button size="sm" variant="outline" asChild>
                        <a href={imageUrl} download={`page-${index + 1}`}>
                          Download
                        </a>
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-muted-foreground">{result.message}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {!isPremium && (
        <Card className="border-primary/20 bg-primary/5">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="w-5 h-5 text-primary" />
              Unlock Premium Features
            </CardTitle>
            <CardDescription>
              Get access to advanced PDF tools, unlimited processing, and priority support for just $39/year.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">∞</div>
                <div className="text-sm text-muted-foreground">Unlimited Processing</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">50MB</div>
                <div className="text-sm text-muted-foreground">Max File Size</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">OCR</div>
                <div className="text-sm text-muted-foreground">Text Extraction</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">24/7</div>
                <div className="text-sm text-muted-foreground">Priority Support</div>
              </div>
            </div>
            <Button asChild className="w-full">
              <a href="/subscribe">
                Upgrade to Premium - $39/year
              </a>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}