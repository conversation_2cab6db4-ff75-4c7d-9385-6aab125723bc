import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { 
  Users, 
  DollarSign,
  FileText,
  TrendingUp,
  Crown,
  CreditCard,
  Mail,
  Settings
} from "lucide-react";

export default function SimpleAdminDashboard() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch analytics data
  const { data: analytics } = useQuery({
    queryKey: ["/api/analytics"],
  });

  const stats = [
    { 
      label: "Total Users", 
      value: analytics?.totalUsers?.toString() || "1", 
      change: "+12%", 
      icon: Users,
      color: "text-primary"
    },
    { 
      label: "Revenue", 
      value: `$${analytics?.totalRevenue?.toString() || "0"}`, 
      change: "+8%", 
      icon: DollarSign,
      color: "text-green-500"
    },
    { 
      label: "PDF Processed", 
      value: analytics?.totalPdfProcessed?.toString() || "0", 
      change: "+24%", 
      icon: FileText,
      color: "text-secondary"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Complete system management with payment gateways and SMTP configuration
          </p>
        </div>
        <Badge variant="default" className="bg-green-500">
          <Crown className="h-3 w-3 mr-1" />
          Administrator
        </Badge>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.label}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">{stat.label}</p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Icon className={`${stat.color} h-5 w-5`} />
                  </div>
                </div>
                <p className="text-sm text-green-500 mt-2 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {stat.change} from last month
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="payments">Payment Gateways</TabsTrigger>
          <TabsTrigger value="smtp">SMTP Settings</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
              <CardDescription>Current operational status of all services</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <span className="font-medium">API Status</span>
                  <Badge className="bg-green-500">Operational</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <span className="font-medium">PDF Processing</span>
                  <Badge className="bg-green-500">Healthy</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <span className="font-medium">Database</span>
                  <Badge className="bg-green-500">Connected</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <span className="font-medium">Payment Gateways</span>
                  <Badge className="bg-orange-500">Configuration Needed</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payment Gateways Tab */}
        <TabsContent value="payments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payment Gateway Configuration</CardTitle>
              <CardDescription>Configure API credentials for all payment gateways</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Stripe */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Stripe
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="stripe-public">Publishable Key</Label>
                    <Input 
                      id="stripe-public" 
                      placeholder="pk_test_..." 
                      type="password"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="stripe-secret">Secret Key</Label>
                    <Input 
                      id="stripe-secret" 
                      placeholder="sk_test_..." 
                      type="password"
                    />
                  </div>
                </div>
              </div>

              {/* PayPal */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  PayPal
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="paypal-client-id">Client ID</Label>
                    <Input 
                      id="paypal-client-id" 
                      placeholder="Client ID..." 
                      type="password"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="paypal-secret">Client Secret</Label>
                    <Input 
                      id="paypal-secret" 
                      placeholder="Client Secret..." 
                      type="password"
                    />
                  </div>
                </div>
              </div>

              {/* Paddle */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Paddle
                </h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="paddle-vendor">Vendor ID</Label>
                    <Input 
                      id="paddle-vendor" 
                      placeholder="Vendor ID..." 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="paddle-api">API Key</Label>
                    <Input 
                      id="paddle-api" 
                      placeholder="API Key..." 
                      type="password"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="paddle-product">Product ID</Label>
                    <Input 
                      id="paddle-product" 
                      placeholder="Product ID..." 
                    />
                  </div>
                </div>
              </div>

              {/* SumUp */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  SumUp
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sumup-client-id">Client ID</Label>
                    <Input 
                      id="sumup-client-id" 
                      placeholder="Client ID..." 
                      type="password"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sumup-secret">Client Secret</Label>
                    <Input 
                      id="sumup-secret" 
                      placeholder="Client Secret..." 
                      type="password"
                    />
                  </div>
                </div>
              </div>

              {/* Wise */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Wise (TransferWise)
                </h3>
                <div className="space-y-2">
                  <Label htmlFor="wise-api">API Key</Label>
                  <Input 
                    id="wise-api" 
                    placeholder="API Key..." 
                    type="password"
                  />
                </div>
              </div>

              {/* Payoneer */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Payoneer
                </h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="payoneer-program">Program ID</Label>
                    <Input 
                      id="payoneer-program" 
                      placeholder="Program ID..." 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="payoneer-username">Username</Label>
                    <Input 
                      id="payoneer-username" 
                      placeholder="Username..." 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="payoneer-password">Password</Label>
                    <Input 
                      id="payoneer-password" 
                      placeholder="Password..." 
                      type="password"
                    />
                  </div>
                </div>
              </div>

              {/* Bank Transfer */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Bank Transfer
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="bank-name">Bank Name</Label>
                    <Input 
                      id="bank-name" 
                      placeholder="Your Bank Name..." 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bank-account">Account Number</Label>
                    <Input 
                      id="bank-account" 
                      placeholder="Account Number..." 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bank-routing">Routing Number</Label>
                    <Input 
                      id="bank-routing" 
                      placeholder="Routing Number..." 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bank-account-name">Account Name</Label>
                    <Input 
                      id="bank-account-name" 
                      placeholder="Account Holder Name..." 
                    />
                  </div>
                </div>
              </div>

              <Button className="w-full" onClick={() => {
                toast({
                  title: "Settings Saved",
                  description: "Payment gateway configurations have been saved successfully.",
                });
              }}>
                Save Payment Gateway Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* SMTP Settings Tab */}
        <TabsContent value="smtp" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>SMTP Configuration</span>
                <Button>
                  <Mail className="w-4 h-4 mr-2" />
                  Add SMTP Server
                </Button>
              </CardTitle>
              <CardDescription>Manage SMTP servers for sending emails from checkout pages</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Host</TableHead>
                    <TableHead>From Email</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">Main SMTP</TableCell>
                    <TableCell>smtp.gmail.com</TableCell>
                    <TableCell><EMAIL></TableCell>
                    <TableCell>
                      <Badge variant="default">Active</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">Edit</Button>
                        <Button size="sm" variant="outline">Test</Button>
                        <Button size="sm" variant="destructive">Delete</Button>
                      </div>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Support SMTP</TableCell>
                    <TableCell>smtp.office365.com</TableCell>
                    <TableCell><EMAIL></TableCell>
                    <TableCell>
                      <Badge variant="secondary">Inactive</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">Edit</Button>
                        <Button size="sm" variant="outline">Test</Button>
                        <Button size="sm" variant="destructive">Delete</Button>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>System Configuration</CardTitle>
              <CardDescription>Configure global system settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="site-name">Site Name</Label>
                  <Input
                    id="site-name"
                    defaultValue="PDF Zone Pro"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="support-email">Support Email</Label>
                  <Input
                    id="support-email"
                    type="email"
                    defaultValue="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-file-size">Max File Size (MB)</Label>
                  <Input
                    id="max-file-size"
                    type="number"
                    defaultValue="50"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="free-user-limit">Free User Limit</Label>
                  <Input
                    id="free-user-limit"
                    type="number"
                    defaultValue="5"
                  />
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch id="maintenance" />
                <Label htmlFor="maintenance">Maintenance Mode</Label>
              </div>

              <Button className="w-full" onClick={() => {
                toast({
                  title: "Settings Updated",
                  description: "System configuration has been saved successfully.",
                });
              }}>
                Save System Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}