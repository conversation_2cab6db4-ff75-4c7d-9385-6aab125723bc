import { Router } from "express";
import { z } from "zod";
import { storage } from "../storage";
import { requireAuth } from "../middleware/auth";
import { insertInvoiceSchema, updateInvoiceSchema } from "@shared/schema";

const router = Router();

// Admin routes - require authentication
router.use(requireAuth);

// Get all invoices
router.get("/", async (req, res) => {
  try {
    const invoices = await storage.getInvoices();
    res.json(invoices);
  } catch (error) {
    console.error("Error fetching invoices:", error);
    res.status(500).json({ error: "Failed to fetch invoices" });
  }
});

// Get invoice by number
router.get("/:invoiceNumber", async (req, res) => {
  try {
    const { invoiceNumber } = req.params;
    const invoice = await storage.getInvoiceByNumber(invoiceNumber);
    
    if (!invoice) {
      return res.status(404).json({ error: "Invoice not found" });
    }
    
    res.json(invoice);
  } catch (error) {
    console.error("Error fetching invoice:", error);
    res.status(500).json({ error: "Failed to fetch invoice" });
  }
});

// Create new invoice
router.post("/", async (req, res) => {
  try {
    const validatedData = insertInvoiceSchema.parse(req.body);
    
    // Generate invoice number if not provided
    if (!validatedData.invoiceNumber) {
      validatedData.invoiceNumber = await storage.generateInvoiceNumber();
    }
    
    const invoice = await storage.createInvoice(validatedData);
    res.status(201).json(invoice);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: "Invalid invoice data", details: error.errors });
    }
    console.error("Error creating invoice:", error);
    res.status(500).json({ error: "Failed to create invoice" });
  }
});

// Update invoice
router.put("/:invoiceNumber", async (req, res) => {
  try {
    const { invoiceNumber } = req.params;
    const validatedData = updateInvoiceSchema.parse(req.body);
    
    const invoice = await storage.updateInvoice(invoiceNumber, validatedData);
    res.json(invoice);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: "Invalid invoice data", details: error.errors });
    }
    console.error("Error updating invoice:", error);
    res.status(500).json({ error: "Failed to update invoice" });
  }
});

// Delete invoice
router.delete("/:invoiceNumber", async (req, res) => {
  try {
    const { invoiceNumber } = req.params;
    await storage.deleteInvoice(invoiceNumber);
    res.status(204).send();
  } catch (error) {
    console.error("Error deleting invoice:", error);
    res.status(500).json({ error: "Failed to delete invoice" });
  }
});

// Generate invoice from payment
router.post("/generate/:paymentId", async (req, res) => {
  try {
    const { paymentId } = req.params;
    const payment = await storage.getPaymentById(parseInt(paymentId));
    
    if (!payment) {
      return res.status(404).json({ error: "Payment not found" });
    }
    
    // Check if invoice already exists for this payment
    const existingInvoices = await storage.getInvoicesByPayment(payment.id);
    if (existingInvoices.length > 0) {
      return res.json(existingInvoices[0]);
    }
    
    // Get customer data from payment
    const customerData = payment.customerData ? JSON.parse(payment.customerData) : {};
    
    // Get checkout page for product info
    const checkoutPage = payment.checkoutPageId 
      ? await storage.getCheckoutPageById(payment.checkoutPageId)
      : null;
    
    // Generate invoice number
    const invoiceNumber = await storage.generateInvoiceNumber();
    
    // Create invoice data
    const invoiceData = {
      invoiceNumber,
      paymentId: payment.id,
      customerName: customerData.name || customerData.firstName + ' ' + customerData.lastName || 'Customer',
      customerEmail: customerData.email || '<EMAIL>',
      customerPhone: customerData.phone || null,
      customerAddress: customerData.address || null,
      companyName: customerData.company || null,
      taxId: customerData.taxId || null,
      productName: checkoutPage?.title || 'PDF Tools Pro Service',
      productDescription: checkoutPage?.description || 'Professional PDF processing service',
      quantity: 1,
      unitPrice: parseFloat(payment.amount),
      subtotal: parseFloat(payment.amount),
      taxRate: 0,
      taxAmount: 0,
      total: parseFloat(payment.amount),
      currency: payment.currency,
      dueDate: null, // Already paid
      status: payment.status === 'completed' ? 'paid' : 'draft',
      notes: `Generated from payment ${payment.gatewayTransactionId || payment.id}`
    };
    
    const invoice = await storage.createInvoice(invoiceData);
    
    // Update payment to mark invoice as generated
    await storage.updatePayment(payment.id, { 
      invoiceNumber: invoice.invoiceNumber,
      invoiceGenerated: true 
    });
    
    res.status(201).json(invoice);
  } catch (error) {
    console.error("Error generating invoice from payment:", error);
    res.status(500).json({ error: "Failed to generate invoice" });
  }
});

// Get invoices for a specific payment
router.get("/payment/:paymentId", async (req, res) => {
  try {
    const { paymentId } = req.params;
    const invoices = await storage.getInvoicesByPayment(parseInt(paymentId));
    res.json(invoices);
  } catch (error) {
    console.error("Error fetching invoices for payment:", error);
    res.status(500).json({ error: "Failed to fetch invoices" });
  }
});

export default router;
