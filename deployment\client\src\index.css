@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import custom animations */
@import './styles/animations.css';

:root {
  --background: 0 0% 100%; /* #FFFFFF */
  --foreground: 222 84% 20%; /* #1A1F36 */
  --muted: 210 40% 96%; /* #F5F7FA */
  --muted-foreground: 215 16% 47%; /* #6B7280 */
  --popover: 0 0% 100%; /* #FFFFFF */
  --popover-foreground: 222 84% 20%; /* #1A1F36 */
  --card: 0 0% 100%; /* #FFFFFF */
  --card-foreground: 222 84% 20%; /* #1A1F36 */
  --border: 214 32% 91%; /* #E5E7EB */
  --input: 214 32% 91%; /* #E5E7EB */
  --primary: 250 84% 67%; /* #635BFF */
  --primary-foreground: 0 0% 100%; /* #FFFFFF */
  --secondary: 196 100% 67%; /* #00D4FF */
  --secondary-foreground: 222 84% 20%; /* #1A1F36 */
  --accent: 210 40% 96%; /* #F5F7FA */
  --accent-foreground: 222 84% 20%; /* #1A1F36 */
  --destructive: 0 84% 60%; /* #EF4444 */
  --destructive-foreground: 0 0% 98%; /* #FAFAFA */
  --success: 142 71% 52%; /* #32D583 */
  --ring: 250 84% 67%; /* #635BFF */
  --radius: 0.5rem;
  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 217.2 91.2% 59.8%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
}

.dark {
  --background: 222 84% 5%; /* #0A0E1A */
  --foreground: 210 40% 98%; /* #F9FAFB */
  --muted: 217 32% 18%; /* #1F2937 */
  --muted-foreground: 215 20% 65%; /* #9CA3AF */
  --popover: 222 84% 5%; /* #0A0E1A */
  --popover-foreground: 210 40% 98%; /* #F9FAFB */
  --card: 222 84% 5%; /* #0A0E1A */
  --card-foreground: 210 40% 98%; /* #F9FAFB */
  --border: 217 32% 18%; /* #1F2937 */
  --input: 217 32% 18%; /* #1F2937 */
  --primary: 250 84% 67%; /* #635BFF */
  --primary-foreground: 0 0% 100%; /* #FFFFFF */
  --secondary: 196 100% 67%; /* #00D4FF */
  --secondary-foreground: 222 84% 20%; /* #1A1F36 */
  --accent: 217 32% 18%; /* #1F2937 */
  --accent-foreground: 210 40% 98%; /* #F9FAFB */
  --destructive: 0 63% 31%; /* #7F1D1D */
  --destructive-foreground: 210 40% 98%; /* #F9FAFB */
  --success: 142 71% 52%; /* #32D583 */
  --ring: 250 84% 67%; /* #635BFF */
  --sidebar-background: 240 5.9% 10%;
  --sidebar-foreground: 240 4.8% 95.9%;
  --sidebar-primary: 224.3 76.3% 94.1%;
  --sidebar-primary-foreground: 220.9 39.3% 11%;
  --sidebar-accent: 240 3.7% 15.9%;
  --sidebar-accent-foreground: 240 4.8% 95.9%;
  --sidebar-border: 240 3.7% 15.9%;
  --sidebar-ring: 217.2 91.2% 59.8%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'SF Pro Display', 'Inter', system-ui, -apple-system, sans-serif;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.6s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}
