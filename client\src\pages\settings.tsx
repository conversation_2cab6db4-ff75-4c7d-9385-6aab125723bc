import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/hooks/use-user";
import { apiRequest } from "@/lib/queryClient";
import {
  Shield,
  Key,
  Smartphone,
  History,
  Mail,
  User,
  Settings as SettingsIcon,
  Eye,
  EyeOff,
  Copy,
  Trash2,
  Plus,
  QrCode
} from "lucide-react";

export default function Settings() {
  const { data: userData } = useUser();
  const user = userData?.user;
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [profileData, setProfileData] = useState({
    username: user?.username || "",
    email: user?.email || "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  });

  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  const [twoFactorSetup, setTwoFactorSetup] = useState({
    secret: "",
    qrCode: "",
    token: "",
    manualKey: ""
  });

  const [newApiKey, setNewApiKey] = useState({ name: "", key: "" });
  const [showApiKey, setShowApiKey] = useState(false);

  // Fetch user data
  const { data: loginHistory } = useQuery({
    queryKey: ["/api/user/login-history"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/user/login-history");
      return res.json();
    }
  });

  const { data: apiKeys } = useQuery({
    queryKey: ["/api/user/api-keys"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/user/api-keys");
      return res.json();
    }
  });

  // Mutations
  const updateProfileMutation = useMutation({
    mutationFn: async (data: any) => {
      const res = await apiRequest("PUT", "/api/user/profile", data);
      return res.json();
    },
    onSuccess: (data) => {
      queryClient.setQueryData(["/api/auth/me"], data);
      toast({
        title: "Profile Updated",
        description: "Your profile has been updated successfully.",
      });
      setProfileData(prev => ({ ...prev, currentPassword: "", newPassword: "", confirmPassword: "" }));
    },
    onError: (error: any) => {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update profile.",
        variant: "destructive",
      });
    }
  });

  const setup2FAMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("POST", "/api/auth/2fa/setup");
      return res.json();
    },
    onSuccess: (data) => {
      setTwoFactorSetup({
        secret: data.secret,
        qrCode: data.qrCode,
        token: "",
        manualKey: data.manualEntryKey
      });
    }
  });

  const enable2FAMutation = useMutation({
    mutationFn: async (data: { secret: string; token: string }) => {
      const res = await apiRequest("POST", "/api/auth/2fa/enable", data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/auth/me"] });
      toast({
        title: "2FA Enabled",
        description: "Two-factor authentication has been enabled successfully.",
      });
      setTwoFactorSetup({ secret: "", qrCode: "", token: "", manualKey: "" });
    }
  });

  const disable2FAMutation = useMutation({
    mutationFn: async (token: string) => {
      const res = await apiRequest("POST", "/api/auth/2fa/disable", { token });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/auth/me"] });
      toast({
        title: "2FA Disabled",
        description: "Two-factor authentication has been disabled.",
      });
    }
  });

  const createApiKeyMutation = useMutation({
    mutationFn: async (name: string) => {
      const res = await apiRequest("POST", "/api/user/api-keys", { name });
      return res.json();
    },
    onSuccess: (data) => {
      setNewApiKey({ name: "", key: data.key });
      setShowApiKey(true);
      queryClient.invalidateQueries({ queryKey: ["/api/user/api-keys"] });
      toast({
        title: "API Key Created",
        description: "Your new API key has been created. Please save it securely.",
      });
    }
  });

  const deleteApiKeyMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("DELETE", `/api/user/api-keys/${id}`);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/user/api-keys"] });
      toast({
        title: "API Key Deleted",
        description: "The API key has been deleted successfully.",
      });
    }
  });

  const handleProfileUpdate = () => {
    const updates: any = {};

    if (profileData.username !== user?.username) {
      updates.username = profileData.username;
    }

    if (profileData.email !== user?.email) {
      updates.email = profileData.email;
    }

    if (profileData.newPassword) {
      if (profileData.newPassword !== profileData.confirmPassword) {
        toast({
          title: "Password Mismatch",
          description: "New passwords do not match.",
          variant: "destructive",
        });
        return;
      }
      updates.currentPassword = profileData.currentPassword;
      updates.newPassword = profileData.newPassword;
    }

    if (Object.keys(updates).length === 0) {
      toast({
        title: "No Changes",
        description: "No changes to update.",
      });
      return;
    }

    updateProfileMutation.mutate(updates);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Copied to clipboard.",
    });
  };

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Account Settings</h1>
          <p className="text-muted-foreground">
            Manage your account security and preferences
          </p>
        </div>
        <Badge variant={user.emailVerified ? "default" : "destructive"}>
          {user.emailVerified ? "Verified" : "Unverified"}
        </Badge>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profile Information
              </CardTitle>
              <CardDescription>
                Update your account information and password
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    value={profileData.username}
                    onChange={(e) => setProfileData(prev => ({ ...prev, username: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profileData.email}
                    onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="text-lg font-medium mb-4">Change Password</h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">Current Password</Label>
                    <div className="relative">
                      <Input
                        id="currentPassword"
                        type={showPasswords.current ? "text" : "password"}
                        value={profileData.currentPassword}
                        onChange={(e) => setProfileData(prev => ({ ...prev, currentPassword: e.target.value }))}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowPasswords(prev => ({ ...prev, current: !prev.current }))}
                      >
                        {showPasswords.current ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="newPassword">New Password</Label>
                      <div className="relative">
                        <Input
                          id="newPassword"
                          type={showPasswords.new ? "text" : "password"}
                          value={profileData.newPassword}
                          onChange={(e) => setProfileData(prev => ({ ...prev, newPassword: e.target.value }))}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3"
                          onClick={() => setShowPasswords(prev => ({ ...prev, new: !prev.new }))}
                        >
                          {showPasswords.new ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword">Confirm New Password</Label>
                      <div className="relative">
                        <Input
                          id="confirmPassword"
                          type={showPasswords.confirm ? "text" : "password"}
                          value={profileData.confirmPassword}
                          onChange={(e) => setProfileData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3"
                          onClick={() => setShowPasswords(prev => ({ ...prev, confirm: !prev.confirm }))}
                        >
                          {showPasswords.confirm ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Button
                onClick={handleProfileUpdate}
                disabled={updateProfileMutation.isPending}
                className="w-full"
              >
                {updateProfileMutation.isPending ? "Updating..." : "Update Profile"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security">
          <div className="space-y-6">
            {/* Email Verification */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Email Verification
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Email Status</p>
                    <p className="text-sm text-muted-foreground">
                      {user.emailVerified ? "Your email is verified" : "Please verify your email address"}
                    </p>
                  </div>
                  <Badge variant={user.emailVerified ? "default" : "destructive"}>
                    {user.emailVerified ? "Verified" : "Unverified"}
                  </Badge>
                </div>
                {!user.emailVerified && (
                  <Button className="mt-4" variant="outline">
                    Resend Verification Email
                  </Button>
                )}
              </CardContent>
            </Card>

            {/* Two-Factor Authentication */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Smartphone className="h-5 w-5" />
                  Two-Factor Authentication
                </CardTitle>
                <CardDescription>
                  Add an extra layer of security to your account
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">2FA Status</p>
                    <p className="text-sm text-muted-foreground">
                      {user.twoFactorEnabled ? "Two-factor authentication is enabled" : "Two-factor authentication is disabled"}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={user.twoFactorEnabled ? "default" : "secondary"}>
                      {user.twoFactorEnabled ? "Enabled" : "Disabled"}
                    </Badge>
                    {user.twoFactorEnabled ? (
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="destructive" size="sm">Disable</Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Disable Two-Factor Authentication</DialogTitle>
                            <DialogDescription>
                              Enter your authenticator code to disable 2FA
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="space-y-2">
                              <Label htmlFor="disable2fa">Authenticator Code</Label>
                              <Input
                                id="disable2fa"
                                placeholder="000000"
                                maxLength={6}
                                value={twoFactorSetup.token}
                                onChange={(e) => setTwoFactorSetup(prev => ({ ...prev, token: e.target.value }))}
                              />
                            </div>
                            <Button
                              onClick={() => disable2FAMutation.mutate(twoFactorSetup.token)}
                              disabled={disable2FAMutation.isPending || twoFactorSetup.token.length !== 6}
                              variant="destructive"
                              className="w-full"
                            >
                              {disable2FAMutation.isPending ? "Disabling..." : "Disable 2FA"}
                            </Button>
                          </div>
                        </DialogContent>
                      </Dialog>
                    ) : (
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button size="sm" onClick={() => setup2FAMutation.mutate()}>Enable</Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-md">
                          <DialogHeader>
                            <DialogTitle>Setup Two-Factor Authentication</DialogTitle>
                            <DialogDescription>
                              Scan the QR code with your authenticator app
                            </DialogDescription>
                          </DialogHeader>
                          {twoFactorSetup.qrCode && (
                            <div className="space-y-4">
                              <div className="flex justify-center">
                                <img src={twoFactorSetup.qrCode} alt="QR Code" className="w-48 h-48" />
                              </div>
                              <div className="space-y-2">
                                <Label>Manual Entry Key</Label>
                                <div className="flex gap-2">
                                  <Input value={twoFactorSetup.manualKey} readOnly />
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => copyToClipboard(twoFactorSetup.manualKey)}
                                  >
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="verify2fa">Verification Code</Label>
                                <Input
                                  id="verify2fa"
                                  placeholder="000000"
                                  maxLength={6}
                                  value={twoFactorSetup.token}
                                  onChange={(e) => setTwoFactorSetup(prev => ({ ...prev, token: e.target.value }))}
                                />
                              </div>
                              <Button
                                onClick={() => enable2FAMutation.mutate({
                                  secret: twoFactorSetup.secret,
                                  token: twoFactorSetup.token
                                })}
                                disabled={enable2FAMutation.isPending || twoFactorSetup.token.length !== 6}
                                className="w-full"
                              >
                                {enable2FAMutation.isPending ? "Enabling..." : "Enable 2FA"}
                              </Button>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* API Keys Tab */}
        <TabsContent value="api-keys">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                API Keys
              </CardTitle>
              <CardDescription>
                Manage your API keys for programmatic access
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium">Your API Keys</h3>
                    <p className="text-sm text-muted-foreground">
                      Use these keys to access the API programmatically
                    </p>
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Create API Key
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create New API Key</DialogTitle>
                        <DialogDescription>
                          Give your API key a descriptive name
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="apiKeyName">API Key Name</Label>
                          <Input
                            id="apiKeyName"
                            placeholder="My API Key"
                            value={newApiKey.name}
                            onChange={(e) => setNewApiKey(prev => ({ ...prev, name: e.target.value }))}
                          />
                        </div>
                        <Button
                          onClick={() => createApiKeyMutation.mutate(newApiKey.name)}
                          disabled={createApiKeyMutation.isPending || !newApiKey.name.trim()}
                          className="w-full"
                        >
                          {createApiKeyMutation.isPending ? "Creating..." : "Create API Key"}
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>

                {/* API Key Display Dialog */}
                <Dialog open={showApiKey} onOpenChange={setShowApiKey}>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>API Key Created</DialogTitle>
                      <DialogDescription>
                        Please save this API key. You won't be able to see it again.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>Your API Key</Label>
                        <div className="flex gap-2">
                          <Input value={newApiKey.key} readOnly />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(newApiKey.key)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <Button onClick={() => setShowApiKey(false)} className="w-full">
                        I've Saved My Key
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>

                {/* API Keys Table */}
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Last Used</TableHead>
                        <TableHead>Usage Count</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {apiKeys?.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center text-muted-foreground">
                            No API keys found
                          </TableCell>
                        </TableRow>
                      ) : (
                        apiKeys?.map((key: any) => (
                          <TableRow key={key.id}>
                            <TableCell className="font-medium">{key.name}</TableCell>
                            <TableCell>
                              {key.lastUsed ? new Date(key.lastUsed).toLocaleDateString() : "Never"}
                            </TableCell>
                            <TableCell>{key.usageCount}</TableCell>
                            <TableCell>{new Date(key.createdAt).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => deleteApiKeyMutation.mutate(key.id)}
                                disabled={deleteApiKeyMutation.isPending}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Login History
              </CardTitle>
              <CardDescription>
                Recent login activity on your account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Status</TableHead>
                      <TableHead>IP Address</TableHead>
                      <TableHead>User Agent</TableHead>
                      <TableHead>Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loginHistory?.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center text-muted-foreground">
                          No login history found
                        </TableCell>
                      </TableRow>
                    ) : (
                      loginHistory?.map((entry: any) => (
                        <TableRow key={entry.id}>
                          <TableCell>
                            <Badge variant={entry.success ? "default" : "destructive"}>
                              {entry.success ? "Success" : "Failed"}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-mono text-sm">{entry.ipAddress}</TableCell>
                          <TableCell className="text-sm truncate max-w-xs">
                            {entry.userAgent || "Unknown"}
                          </TableCell>
                          <TableCell>{new Date(entry.createdAt).toLocaleString()}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
