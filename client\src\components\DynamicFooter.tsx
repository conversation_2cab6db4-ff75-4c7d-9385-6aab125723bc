import { useEffect, useState } from "react";
import { <PERSON> } from "wouter";
import { Shield } from "lucide-react";

interface FooterLink {
  slug: string;
  title: string;
  sortOrder: number;
}

interface FooterLinks {
  product?: FooterLink[];
  support?: FooterLink[];
  legal?: FooterLink[];
  company?: FooterLink[];
  [key: string]: FooterLink[] | undefined;
}

export default function DynamicFooter() {
  const [footerLinks, setFooterLinks] = useState<FooterLinks>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFooterLinks();
  }, []);

  const fetchFooterLinks = async () => {
    try {
      const response = await fetch("/api/pages/footer/links");
      if (response.ok) {
        const data = await response.json();
        setFooterLinks(data);
      }
    } catch (error) {
      console.error("Failed to fetch footer links:", error);
    } finally {
      setLoading(false);
    }
  };

  // Default static links that are always shown
  const staticLinks = {
    product: [
      { slug: "tools", title: "PDF Zone", sortOrder: 0 },
      { slug: "pricing", title: "Pricing", sortOrder: 1 }
    ],
    support: [
      { slug: "contact", title: "Contact Support", sortOrder: 0 }
    ],
    company: [
      { slug: "about", title: "About Us", sortOrder: 0 }
    ],
    legal: [
      { slug: "privacy", title: "Privacy Policy", sortOrder: 0 },
      { slug: "terms", title: "Terms of Service", sortOrder: 1 }
    ]
  };

  // Merge static links with dynamic links
  const mergeLinks = (section: string) => {
    const static_links = staticLinks[section as keyof typeof staticLinks] || [];
    const dynamic_links = footerLinks[section] || [];
    
    return [...static_links, ...dynamic_links]
      .sort((a, b) => a.sortOrder - b.sortOrder);
  };

  const sections = [
    { key: "product", title: "Product" },
    { key: "support", title: "Support" },
    { key: "legal", title: "Legal" },
    { key: "company", title: "Company" }
  ];

  if (loading) {
    return (
      <footer className="bg-foreground text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-center">
            <div className="animate-spin w-6 h-6 border-2 border-white border-t-transparent rounded-full" />
          </div>
        </div>
      </footer>
    );
  }

  return (
    <footer className="bg-foreground text-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="md:col-span-1">
            <div className="flex items-center mb-4">
              <span className="text-2xl font-bold">PDF Zone Pro</span>
            </div>
            <p className="text-gray-300 mb-4">
              Professional PDF toolkit with custom checkout page generation for businesses and creators.
            </p>
          </div>

          {sections.map((section) => {
            const links = mergeLinks(section.key);
            
            if (links.length === 0) return null;

            return (
              <div key={section.key}>
                <h4 className="font-semibold mb-4">{section.title}</h4>
                <ul className="space-y-2 text-gray-300">
                  {links.map((link) => (
                    <li key={link.slug}>
                      <Link href={`/${link.slug}`}>
                        <span className="hover:text-white cursor-pointer">
                          {link.title}
                        </span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>

        <div className="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-300 text-sm">
            © 2024 PDF Zone Pro. All rights reserved.
          </p>
          <div className="flex items-center space-x-4 mt-4 md:mt-0">
            <span className="text-gray-300 text-sm">Secured by</span>
            <div className="flex items-center space-x-2">
              <Shield className="text-green-500 h-4 w-4" />
              <span className="text-sm">256-bit SSL</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
