import { Request, Response } from "express";

// Additional Payment Gateway Integrations

export interface PaymentGateway {
  name: string;
  isConfigured: boolean;
  createPayment: (amount: number, currency: string, metadata?: any) => Promise<any>;
  verifyPayment: (paymentId: string) => Promise<any>;
}

// Paddle Integration
export class PaddleGateway implements PaymentGateway {
  name = "Paddle";
  isConfigured: boolean;
  private vendorId: string | undefined;
  private apiKey: string | undefined;

  constructor() {
    this.vendorId = process.env.PADDLE_VENDOR_ID;
    this.apiKey = process.env.PADDLE_API_KEY;
    this.isConfigured = !!(this.vendorId && this.apiKey);

    if (!this.isConfigured) {
      console.warn("Paddle credentials not found. Paddle payments will be disabled.");
    }
  }

  async createPayment(amount: number, currency: string, metadata?: any) {
    if (!this.isConfigured) {
      throw new Error("Paddle not configured");
    }

    // Paddle checkout implementation
    return {
      vendor: this.vendorId,
      product: process.env.PADDLE_PRODUCT_ID,
      prices: [`${currency.toLowerCase()}:${amount}`],
      passthrough: JSON.stringify(metadata),
      success: `${process.env.BASE_URL}/payment/success`,
      cancel: `${process.env.BASE_URL}/payment/cancel`
    };
  }

  async verifyPayment(paymentId: string) {
    // Paddle webhook verification
    return { status: "verified", paymentId };
  }
}

// SumUp Integration
export class SumUpGateway implements PaymentGateway {
  name = "SumUp";
  isConfigured: boolean;
  private clientId: string | undefined;
  private clientSecret: string | undefined;

  constructor() {
    this.clientId = process.env.SUMUP_CLIENT_ID;
    this.clientSecret = process.env.SUMUP_CLIENT_SECRET;
    this.isConfigured = !!(this.clientId && this.clientSecret);

    if (!this.isConfigured) {
      console.warn("SumUp credentials not found. SumUp payments will be disabled.");
    }
  }

  async createPayment(amount: number, currency: string, metadata?: any) {
    if (!this.isConfigured) {
      throw new Error("SumUp not configured");
    }

    const response = await fetch("https://api.sumup.com/v0.1/checkouts", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${await this.getAccessToken()}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        checkout_reference: `order_${Date.now()}`,
        amount: amount,
        currency: currency.toUpperCase(),
        description: metadata?.description || "PDF Tools Service",
        return_url: `${process.env.BASE_URL}/payment/success`,
        merchant_code: this.clientId
      })
    });

    return await response.json();
  }

  async verifyPayment(paymentId: string) {
    const response = await fetch(`https://api.sumup.com/v0.1/checkouts/${paymentId}`, {
      headers: {
        "Authorization": `Bearer ${await this.getAccessToken()}`
      }
    });
    return await response.json();
  }

  private async getAccessToken() {
    // SumUp OAuth token flow
    const response = await fetch("https://api.sumup.com/token", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams({
        grant_type: "client_credentials",
        client_id: this.clientId!,
        client_secret: this.clientSecret!
      })
    });
    const data = await response.json();
    return data.access_token;
  }
}

// Wise Integration
export class WiseGateway implements PaymentGateway {
  name = "Wise";
  isConfigured: boolean;
  private apiKey: string | undefined;

  constructor() {
    this.apiKey = process.env.WISE_API_KEY;
    this.isConfigured = !!this.apiKey;

    if (!this.isConfigured) {
      console.warn("Wise credentials not found. Wise payments will be disabled.");
    }
  }

  async createPayment(amount: number, currency: string, metadata?: any) {
    if (!this.isConfigured) {
      throw new Error("Wise not configured");
    }

    // Wise payment link creation
    const response = await fetch("https://api.transferwise.com/v1/payment-links", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${this.apiKey}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        amount: amount,
        currency: currency.toUpperCase(),
        description: metadata?.description || "PDF Zone Pro",
        reference: `pdf_zone_${Date.now()}`,
        redirect_url: `${process.env.BASE_URL}/payment/success`
      })
    });

    return await response.json();
  }

  async verifyPayment(paymentId: string) {
    const response = await fetch(`https://api.transferwise.com/v1/payment-links/${paymentId}`, {
      headers: {
        "Authorization": `Bearer ${this.apiKey}`
      }
    });
    return await response.json();
  }
}

// Payoneer Integration
export class PayoneerGateway implements PaymentGateway {
  name = "Payoneer";
  isConfigured: boolean;
  private programId: string | undefined;
  private username: string | undefined;
  private password: string | undefined;

  constructor() {
    this.programId = process.env.PAYONEER_PROGRAM_ID;
    this.username = process.env.PAYONEER_USERNAME;
    this.password = process.env.PAYONEER_PASSWORD;
    this.isConfigured = !!(this.programId && this.username && this.password);

    if (!this.isConfigured) {
      console.warn("Payoneer credentials not found. Payoneer payments will be disabled.");
    }
  }

  async createPayment(amount: number, currency: string, metadata?: any) {
    if (!this.isConfigured) {
      throw new Error("Payoneer not configured");
    }

    // Payoneer checkout session creation
    const response = await fetch("https://api.payoneer.com/v2/checkout/sessions", {
      method: "POST",
      headers: {
        "Authorization": `Basic ${Buffer.from(`${this.username}:${this.password}`).toString('base64')}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        transactionId: `pdf_${Date.now()}`,
        amount: amount,
        currency: currency.toUpperCase(),
        country: "US",
        callback: {
          successUrl: `${process.env.BASE_URL}/payment/success`,
          errorUrl: `${process.env.BASE_URL}/payment/error`,
          processingUrl: `${process.env.BASE_URL}/payment/processing`
        }
      })
    });

    return await response.json();
  }

  async verifyPayment(paymentId: string) {
    const response = await fetch(`https://api.payoneer.com/v2/checkout/sessions/${paymentId}`, {
      headers: {
        "Authorization": `Basic ${Buffer.from(`${this.username}:${this.password}`).toString('base64')}`
      }
    });
    return await response.json();
  }
}

// PayPal Standard Gateway (for simple PayPal payments)
export class PayPalStandardGateway implements PaymentGateway {
  name = "PayPal Standard";
  isConfigured: boolean;
  private businessEmail: string | undefined;
  private sandboxMode: boolean;

  constructor() {
    this.businessEmail = process.env.PAYPAL_BUSINESS_EMAIL;
    this.sandboxMode = process.env.NODE_ENV !== "production";
    this.isConfigured = !!this.businessEmail;

    if (!this.isConfigured) {
      console.warn("PayPal Standard credentials not found. PayPal Standard payments will be disabled.");
    }
  }

  async createPayment(amount: number, currency: string, metadata?: any) {
    if (!this.isConfigured) {
      throw new Error("PayPal Standard not configured");
    }

    const baseUrl = this.sandboxMode
      ? "https://www.sandbox.paypal.com/cgi-bin/webscr"
      : "https://www.paypal.com/cgi-bin/webscr";

    const returnUrl = `${process.env.BASE_URL || 'http://localhost:5000'}/payment/success`;
    const cancelUrl = `${process.env.BASE_URL || 'http://localhost:5000'}/payment/cancel`;
    const notifyUrl = `${process.env.BASE_URL || 'http://localhost:5000'}/api/paypal/ipn`;

    return {
      type: "paypal_standard",
      paymentUrl: baseUrl,
      formData: {
        cmd: "_xclick",
        business: this.businessEmail,
        item_name: metadata?.productName || "PDF Tools Service",
        amount: amount.toString(),
        currency_code: currency.toUpperCase(),
        return: returnUrl,
        cancel_return: cancelUrl,
        notify_url: notifyUrl,
        custom: JSON.stringify(metadata),
        no_shipping: "1",
        no_note: "1"
      }
    };
  }

  async verifyPayment(paymentId: string) {
    // PayPal Standard uses IPN for verification
    return { status: "pending_verification", paymentId };
  }
}

// Bank Transfer Gateway
export class BankTransferGateway implements PaymentGateway {
  name = "Bank Transfer";
  isConfigured = true; // Always available

  async createPayment(amount: number, currency: string, metadata?: any) {
    // Generate payment instructions
    return {
      paymentId: `bank_${Date.now()}`,
      instructions: {
        bankName: process.env.BANK_NAME || "Your Bank",
        accountNumber: process.env.BANK_ACCOUNT_NUMBER || "XXXX-XXXX-XXXX",
        routingNumber: process.env.BANK_ROUTING_NUMBER || "XXXXXXXXX",
        accountName: process.env.BANK_ACCOUNT_NAME || "PDF Tools Service",
        amount: amount,
        currency: currency.toUpperCase(),
        reference: `PDF-${Date.now()}`,
        instructions: "Please include the reference number in your transfer description"
      }
    };
  }

  async verifyPayment(paymentId: string) {
    // Manual verification required for bank transfers
    return { status: "pending_verification", paymentId };
  }
}

// Cash on Delivery Gateway
export class CashOnDeliveryGateway implements PaymentGateway {
  name = "Cash on Delivery";
  isConfigured = true; // Always available

  async createPayment(amount: number, currency: string, metadata?: any) {
    const orderId = `COD-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const customerData = metadata?.customerData || {};

    return {
      type: "cod",
      paymentId: orderId,
      orderId: orderId,
      amount: amount,
      currency: currency.toUpperCase(),
      status: "confirmed",
      requiresDelivery: true,
      paymentDue: true,
      customerData: customerData,
      deliveryAddress: customerData.address || metadata?.deliveryAddress || '',
      orderDetails: {
        orderNumber: orderId,
        amount: amount,
        currency: currency.toUpperCase(),
        paymentMethod: 'Cash on Delivery',
        orderDate: new Date().toISOString(),
        estimatedDelivery: this.calculateDeliveryDate(),
        deliveryInstructions: [
          'Your order has been confirmed and will be delivered to your address.',
          'Please keep the exact amount ready for payment upon delivery.',
          'Our delivery agent will collect the payment when your order arrives.',
          'You can pay in cash or by card to our delivery agent.',
          'Delivery typically takes 2-5 business days depending on your location.'
        ]
      },
      contactInfo: {
        name: customerData.name || customerData.firstName + ' ' + customerData.lastName || '',
        phone: customerData.phone || '',
        email: customerData.email || '',
        address: customerData.address || metadata?.deliveryAddress || ''
      },
      instructions: `Order confirmed! Your order ${orderId} will be delivered to your address. Please keep ${amount} ${currency.toUpperCase()} ready for payment upon delivery.`
    };
  }

  async verifyPayment(paymentId: string) {
    // COD payments are verified upon delivery
    return {
      status: "pending_delivery",
      paymentId,
      message: "Payment will be collected upon delivery"
    };
  }

  private calculateDeliveryDate(): string {
    const deliveryDate = new Date();
    deliveryDate.setDate(deliveryDate.getDate() + 3); // 3 days from now
    return deliveryDate.toISOString().split('T')[0]; // Return YYYY-MM-DD format
  }
}

// Payment Gateway Manager
export class PaymentGatewayManager {
  private gateways: Map<string, PaymentGateway> = new Map();

  constructor() {
    this.gateways.set("paddle", new PaddleGateway());
    this.gateways.set("sumup", new SumUpGateway());
    this.gateways.set("wise", new WiseGateway());
    this.gateways.set("payoneer", new PayoneerGateway());
    this.gateways.set("paypal-standard", new PayPalStandardGateway());
    this.gateways.set("bank", new BankTransferGateway());
    this.gateways.set("cod", new CashOnDeliveryGateway());
  }

  getGateway(name: string): PaymentGateway | undefined {
    return this.gateways.get(name.toLowerCase());
  }

  getAvailableGateways(): PaymentGateway[] {
    return Array.from(this.gateways.values()).filter(gateway => gateway.isConfigured);
  }

  async createPayment(gateway: string, amount: number, currency: string, metadata?: any) {
    const paymentGateway = this.getGateway(gateway);
    if (!paymentGateway) {
      throw new Error(`Payment gateway ${gateway} not found`);
    }

    if (!paymentGateway.isConfigured) {
      throw new Error(`Payment gateway ${gateway} not configured`);
    }

    return await paymentGateway.createPayment(amount, currency, metadata);
  }

  async verifyPayment(gateway: string, paymentId: string) {
    const paymentGateway = this.getGateway(gateway);
    if (!paymentGateway) {
      throw new Error(`Payment gateway ${gateway} not found`);
    }

    return await paymentGateway.verifyPayment(paymentId);
  }
}

export const paymentGatewayManager = new PaymentGatewayManager();