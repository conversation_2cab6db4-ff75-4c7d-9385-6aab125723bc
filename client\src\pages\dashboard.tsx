import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Link } from "wouter";
import {
  FileText,
  Users,
  DollarSign,
  TrendingUp,
  Crown,
  ShoppingCart,
  Clock,
  CheckCircle,
  XCircle
} from "lucide-react";
import { useUser } from "@/hooks/use-user";

export default function Dashboard() {
  const { data: userData } = useUser();
  const user = userData?.user;

  const { data: analytics } = useQuery({
    queryKey: ["/api/analytics"],
    enabled: !!user,
  });

  const { data: operations, isLoading: operationsLoading, error: operationsError } = useQuery({
    queryKey: ["/api/operations"],
    enabled: !!user,
    retry: 1, // Only retry once to avoid infinite loading
  });

  if (!analytics) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  const isAdmin = user?.role === "admin";
  const isPremium = user?.isPremium || user?.role === 'admin' || user?.username === 'admin' || false;
  const usageLimit = isPremium ? 100 : 5;
  const currentUsage = operations ? operations.filter((op: any) => {
    const today = new Date();
    const opDate = new Date(op.createdAt);
    return opDate.toDateString() === today.toDateString();
  }).length : 0;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "text-green-500";
      case "failed": return "text-red-500";
      default: return "text-yellow-500";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed": return CheckCircle;
      case "failed": return XCircle;
      default: return Clock;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Welcome back, {user?.username}!
          </h1>
          <p className="text-muted-foreground">
            {isAdmin ? "Admin Dashboard" : "Your PDF processing workspace"}
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Badge variant={isPremium ? "default" : "secondary"}>
            {isPremium ? (isAdmin ? "Admin" : "Premium") : "Free"}
          </Badge>
          {!isPremium && (
            <Link href="/subscribe">
              <Button className="bg-gradient-to-r from-primary to-secondary">
                <Crown className="h-4 w-4 mr-2" />
                Upgrade
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* Upgrade Banner for Free Users */}
      {!isPremium && (
        <Card className="bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 border-primary/20">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                  <Crown className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">Unlock Premium Features</h3>
                  <p className="text-sm text-muted-foreground">
                    Get unlimited PDF processing, advanced tools, and priority support for just $39/year
                  </p>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Link href="/pricing">
                  <Button variant="outline" className="border-primary text-primary hover:bg-primary/10">
                    View Plans
                  </Button>
                </Link>
                <Link href="/subscribe">
                  <Button className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90">
                    <Crown className="h-4 w-4 mr-2" />
                    Upgrade Now
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Usage Limits */}
      {!isAdmin && (
        <Card className={!isPremium ? "border-orange-200 bg-orange-50/50" : ""}>
          <CardHeader>
            <CardTitle className="text-lg flex items-center justify-between">
              Daily Usage
              {!isPremium && currentUsage >= usageLimit * 0.8 && (
                <Badge variant="destructive" className="text-xs">
                  Limit Reached
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>PDF Operations</span>
                <span className={currentUsage >= usageLimit ? "text-red-500 font-medium" : ""}>
                  {currentUsage} / {usageLimit}
                </span>
              </div>
              <Progress
                value={(currentUsage / usageLimit) * 100}
                className={`h-3 ${currentUsage >= usageLimit * 0.8 ? "bg-red-100" : ""}`}
              />
              <div className="flex justify-between items-center">
                <p className="text-xs text-muted-foreground">
                  {usageLimit - currentUsage > 0
                    ? `${usageLimit - currentUsage} operations remaining today`
                    : "Daily limit reached"
                  }
                </p>
                {!isPremium && currentUsage >= usageLimit * 0.8 && (
                  <Link href="/subscribe">
                    <Button size="sm" variant="outline" className="text-xs h-7">
                      Upgrade for Unlimited
                    </Button>
                  </Link>
                )}
              </div>
              {!isPremium && (
                <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-xs text-blue-700 font-medium">💡 Premium Benefits:</p>
                  <ul className="text-xs text-blue-600 mt-1 space-y-1">
                    <li>• Unlimited daily operations</li>
                    <li>• Access to all PDF tools</li>
                    <li>• Priority processing</li>
                    <li>• Advanced features</li>
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  {isAdmin ? "Total Users" : "Your PDFs Processed"}
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {isAdmin ? analytics.totalUsers : analytics.totalPdfProcessed}
                </p>
              </div>
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                {isAdmin ? <Users className="text-primary h-5 w-5" /> : <FileText className="text-primary h-5 w-5" />}
              </div>
            </div>
            <p className="text-sm text-green-500 mt-2 flex items-center">
              <TrendingUp className="h-3 w-3 mr-1" />
              +24% from last month
            </p>
          </CardContent>
        </Card>

        {isAdmin && (
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Revenue</p>
                  <p className="text-2xl font-bold text-foreground">${analytics.totalRevenue}</p>
                </div>
                <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                  <DollarSign className="text-green-500 h-5 w-5" />
                </div>
              </div>
              <p className="text-sm text-green-500 mt-2 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +8% from last month
              </p>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  {isAdmin ? "Total PDF Processed" : "Success Rate"}
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {isAdmin
                    ? analytics.totalPdfProcessed
                    : operations
                      ? `${Math.round((operations.filter((op: any) => op.status === 'completed').length / Math.max(operations.length, 1)) * 100)}%`
                      : operationsError
                        ? "N/A"
                        : "Loading..."
                  }
                </p>
              </div>
              <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center">
                <FileText className="text-secondary h-5 w-5" />
              </div>
            </div>
            <p className="text-sm text-green-500 mt-2 flex items-center">
              <TrendingUp className="h-3 w-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Feature Comparison for Free Users Near Limit */}
      {!isPremium && currentUsage >= usageLimit * 0.6 && (
        <Card className="border-amber-200 bg-amber-50/50">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Crown className="h-5 w-5 text-amber-600" />
              You're using {Math.round((currentUsage / usageLimit) * 100)}% of your daily limit
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-foreground mb-3">Free Plan (Current)</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span>5 PDF operations per day</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span>Basic PDF tools only</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span>Standard processing speed</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span>Community support</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-foreground mb-3 flex items-center gap-2">
                  <Crown className="h-4 w-4 text-primary" />
                  Premium Plan
                </h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Unlimited PDF operations</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>All PDF tools & features</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Priority processing</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Priority support</span>
                  </li>
                </ul>
                <div className="mt-4 flex gap-2">
                  <Link href="/pricing">
                    <Button variant="outline" size="sm">
                      Compare Plans
                    </Button>
                  </Link>
                  <Link href="/subscribe">
                    <Button size="sm" className="bg-gradient-to-r from-primary to-secondary">
                      <Crown className="h-3 w-3 mr-1" />
                      Upgrade Now
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Link href="/tools">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <FileText className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground">PDF Tools</h3>
              <p className="text-sm text-muted-foreground">Process PDFs</p>
            </CardContent>
          </Card>
        </Link>

        {/* Only show Checkout Builder to primary admin */}
        {user?.isPrimaryAdmin && (
          <Link href="/checkout-builder">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <ShoppingCart className="h-8 w-8 text-secondary mx-auto mb-2" />
                <h3 className="font-semibold text-foreground">Checkout Builder</h3>
                <p className="text-sm text-muted-foreground">Create pages</p>
              </CardContent>
            </Card>
          </Link>
        )}

        {!isPremium && (
          <Link href="/subscribe">
            <Card className="hover:shadow-lg transition-all duration-300 cursor-pointer border-primary/30 bg-gradient-to-br from-primary/5 to-secondary/5 hover:from-primary/10 hover:to-secondary/10">
              <CardContent className="p-6 text-center relative overflow-hidden">
                <div className="absolute top-2 right-2">
                  <Badge className="bg-gradient-to-r from-primary to-secondary text-white text-xs">
                    Popular
                  </Badge>
                </div>
                <Crown className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold text-foreground">Upgrade to Premium</h3>
                <p className="text-sm text-muted-foreground">Unlimited access</p>
                <p className="text-xs text-primary font-medium mt-1">$48/year</p>
              </CardContent>
            </Card>
          </Link>
        )}

        {isAdmin && (
          <Link href="/admin">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Crown className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <h3 className="font-semibold text-foreground">Admin Panel</h3>
                <p className="text-sm text-muted-foreground">Manage system</p>
              </CardContent>
            </Card>
          </Link>
        )}
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.recentOperations.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">No recent activity</p>
            ) : (
              analytics.recentOperations.map((operation: any) => {
                const StatusIcon = getStatusIcon(operation.status);
                return (
                  <div key={operation.id} className="flex items-center space-x-4 p-3 rounded-lg bg-muted/30">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${operation.status === 'completed' ? 'bg-green-500/10' : operation.status === 'failed' ? 'bg-red-500/10' : 'bg-yellow-500/10'}`}>
                      <StatusIcon className={`h-4 w-4 ${getStatusColor(operation.status)}`} />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-foreground">
                        {operation.operation.charAt(0).toUpperCase() + operation.operation.slice(1)} - {operation.fileName}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(operation.createdAt).toLocaleString()}
                      </p>
                    </div>
                    <Badge variant={operation.status === 'completed' ? 'default' : operation.status === 'failed' ? 'destructive' : 'secondary'}>
                      {operation.status}
                    </Badge>
                  </div>
                );
              })
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
