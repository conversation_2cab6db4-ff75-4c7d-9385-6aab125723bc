import React from 'react';

interface InvoiceData {
  id: number;
  invoiceNumber: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  customerAddress?: string;
  companyName?: string;
  taxId?: string;
  productName: string;
  productDescription?: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  total: number;
  currency: string;
  dueDate?: string;
  paidDate?: string;
  status: string;
  notes?: string;
  createdAt: string;
}

interface InvoicePDFProps {
  invoice: InvoiceData;
  companyInfo?: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website: string;
    logo?: string;
  };
}

export const InvoicePDF: React.FC<InvoicePDFProps> = ({
  invoice,
  companyInfo = {
    name: "PDF Zone Pro",
    address: "123 Business Street\nSuite 100\nBusiness City, BC 12345",
    phone: "+****************",
    email: "<EMAIL>",
    website: "www.pdfzone.pro"
  }
}) => {
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid': return '#10B981';
      case 'sent': return '#3B82F6';
      case 'overdue': return '#EF4444';
      case 'draft': return '#6B7280';
      default: return '#6B7280';
    }
  };

  return (
    <div className="max-w-4xl mx-auto bg-white p-8 shadow-lg" style={{ fontFamily: 'Arial, sans-serif' }}>
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div className="flex items-center space-x-4">
          {/* Company Logo */}
          <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">PDF</span>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{companyInfo.name}</h1>
            <p className="text-gray-600">Professional PDF Solutions</p>
          </div>
        </div>
        <div className="text-right">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">INVOICE</h2>
          <div className="text-sm text-gray-600">
            <p><strong>Invoice #:</strong> {invoice.invoiceNumber}</p>
            <p><strong>Date:</strong> {formatDate(invoice.createdAt)}</p>
            {invoice.dueDate && <p><strong>Due Date:</strong> {formatDate(invoice.dueDate)}</p>}
          </div>
        </div>
      </div>

      {/* Status Badge */}
      <div className="mb-6">
        <span 
          className="inline-block px-3 py-1 rounded-full text-white text-sm font-medium"
          style={{ backgroundColor: getStatusColor(invoice.status) }}
        >
          {invoice.status.toUpperCase()}
        </span>
      </div>

      {/* Company and Customer Info */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">From:</h3>
          <div className="text-sm text-gray-700">
            <p className="font-medium">{companyInfo.name}</p>
            <div className="whitespace-pre-line">{companyInfo.address}</div>
            <p>Phone: {companyInfo.phone}</p>
            <p>Email: {companyInfo.email}</p>
            <p>Website: {companyInfo.website}</p>
          </div>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Bill To:</h3>
          <div className="text-sm text-gray-700">
            <p className="font-medium">{invoice.customerName}</p>
            {invoice.companyName && <p>{invoice.companyName}</p>}
            {invoice.customerAddress && <div className="whitespace-pre-line">{invoice.customerAddress}</div>}
            <p>Email: {invoice.customerEmail}</p>
            {invoice.customerPhone && <p>Phone: {invoice.customerPhone}</p>}
            {invoice.taxId && <p>Tax ID: {invoice.taxId}</p>}
          </div>
        </div>
      </div>

      {/* Invoice Items */}
      <div className="mb-8">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-900">
                Description
              </th>
              <th className="border border-gray-300 px-4 py-3 text-center text-sm font-semibold text-gray-900">
                Qty
              </th>
              <th className="border border-gray-300 px-4 py-3 text-right text-sm font-semibold text-gray-900">
                Unit Price
              </th>
              <th className="border border-gray-300 px-4 py-3 text-right text-sm font-semibold text-gray-900">
                Total
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 px-4 py-3">
                <div>
                  <p className="font-medium text-gray-900">{invoice.productName}</p>
                  {invoice.productDescription && (
                    <p className="text-sm text-gray-600 mt-1">{invoice.productDescription}</p>
                  )}
                </div>
              </td>
              <td className="border border-gray-300 px-4 py-3 text-center">
                {invoice.quantity}
              </td>
              <td className="border border-gray-300 px-4 py-3 text-right">
                {formatCurrency(invoice.unitPrice, invoice.currency)}
              </td>
              <td className="border border-gray-300 px-4 py-3 text-right font-medium">
                {formatCurrency(invoice.subtotal, invoice.currency)}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-64">
          <div className="flex justify-between py-2 border-b border-gray-200">
            <span className="text-gray-700">Subtotal:</span>
            <span className="font-medium">{formatCurrency(invoice.subtotal, invoice.currency)}</span>
          </div>
          {invoice.taxRate > 0 && (
            <div className="flex justify-between py-2 border-b border-gray-200">
              <span className="text-gray-700">Tax ({(invoice.taxRate * 100).toFixed(2)}%):</span>
              <span className="font-medium">{formatCurrency(invoice.taxAmount, invoice.currency)}</span>
            </div>
          )}
          <div className="flex justify-between py-3 border-b-2 border-gray-900">
            <span className="text-lg font-semibold text-gray-900">Total:</span>
            <span className="text-lg font-bold text-gray-900">
              {formatCurrency(invoice.total, invoice.currency)}
            </span>
          </div>
        </div>
      </div>

      {/* Payment Status */}
      {invoice.paidDate && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-800 font-medium">
            ✓ Payment received on {formatDate(invoice.paidDate)}
          </p>
        </div>
      )}

      {/* Notes */}
      {invoice.notes && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Notes:</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-700 whitespace-pre-line">{invoice.notes}</p>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="border-t border-gray-200 pt-6 mt-8">
        <div className="text-center text-sm text-gray-600">
          <p className="mb-2">Thank you for your business!</p>
          <p>For questions about this invoice, please contact us at {companyInfo.email}</p>
          <p className="mt-4 text-xs">
            This invoice was generated automatically by {companyInfo.name} billing system.
          </p>
        </div>
      </div>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          .max-w-4xl {
            max-width: none;
            margin: 0;
            padding: 20px;
            box-shadow: none;
          }
          
          body {
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
          }
          
          .bg-gradient-to-br {
            background: #3B82F6 !important;
          }
          
          .bg-gray-50 {
            background-color: #F9FAFB !important;
          }
          
          .border {
            border: 1px solid #D1D5DB !important;
          }
          
          .border-gray-300 {
            border-color: #D1D5DB !important;
          }
          
          .border-gray-200 {
            border-color: #E5E7EB !important;
          }
          
          .border-gray-900 {
            border-color: #111827 !important;
          }
        }
      `}</style>
    </div>
  );
};

export default InvoicePDF;
