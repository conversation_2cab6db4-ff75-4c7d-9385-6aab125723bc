import { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/hooks/use-user";
import { apiRequest } from "@/lib/queryClient";
import {
  ShoppingCart,
  Eye,
  Plus,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Trash2,
  Edit,
  Truck
} from "lucide-react";

interface CheckoutPage {
  id: number;
  name: string;
  slug: string;
  title: string;
  description: string;
  productName: string;
  price: string;
  currency: string;
  smtpConfigId: number | null;
  features?: string[];
  customerFields: {
    requireName: boolean;
    requireEmail: boolean;
    requirePhone: boolean;
    requireAddress: boolean;
    customFields: Array<{
      label: string;
      type: string;
      required: boolean;
    }>;
  };
  theme: {
    primaryColor: string;
    secondaryColor: string;
    backgroundColor: string;
    textColor: string;
    fontFamily: string;
    layout: string;
    borderRadius: string;
    buttonStyle: string;
  };
  paymentGateways: {
    stripe: boolean;
    paypal: boolean;
    paypalStandard: boolean;
    paddle: boolean;
    sumup: boolean;
    wise: boolean;
    payoneer: boolean;
    bankTransfer: boolean;
    cod: boolean;
  };
  isActive: boolean;
  createdAt: string;
}

export default function CheckoutBuilder() {
  const { data: userData } = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const user = userData?.user;

  const [isCreating, setIsCreating] = useState(false);
  const [selectedPage, setSelectedPage] = useState<CheckoutPage | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [previewMode, setPreviewMode] = useState(false);

  // Form state for new/edit checkout page
  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    title: "",
    description: "",
    productName: "PDF Tools Premium - Annual Subscription",
    price: "",
    currency: "USD",
    smtpConfigId: "",
    features: [
      "All premium features included",
      "Unlimited PDF processing",
      "Priority customer support",
      "30-day money-back guarantee"
    ],
    customerFields: {
      requireName: true,
      requireEmail: true,
      requirePhone: false,
      requireAddress: false,
      customFields: []
    },
    theme: {
      primaryColor: "#635BFF",
      secondaryColor: "#00D4FF",
      backgroundColor: "#FFFFFF",
      textColor: "#1A1F36",
      fontFamily: "Inter",
      layout: "centered",
      borderRadius: "8px",
      buttonStyle: "solid"
    },
    paymentGateways: {
      stripe: true,
      paypal: true,
      paypalStandard: false,
      paddle: false,
      sumup: false,
      wise: false,
      payoneer: false,
      bankTransfer: false,
      cod: false
    }
  });

  const { data: checkoutPages, isLoading } = useQuery({
    queryKey: ["/api/checkout-pages"],
    enabled: !!user,
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/checkout-pages");
      return res.json();
    },
  });

  const { data: smtpConfigs } = useQuery({
    queryKey: ["/api/smtp-configs"],
    enabled: !!user,
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/smtp-configs");
      return res.json();
    },
  });

  const createPageMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/checkout-pages", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/checkout-pages"] });
      setIsCreating(false);
      resetForm();
      toast({
        title: "Checkout page created!",
        description: "Your new checkout page is ready to use.",
      });
    },
    onError: async (error: any) => {
      console.error('Create page error:', error);
      let errorMessage = "Please check your information and try again.";

      try {
        const errorData = await error.response?.json();
        if (errorData?.errors && Array.isArray(errorData.errors)) {
          // Handle validation errors
          const fieldErrors = errorData.errors.map((err: any) => {
            const field = err.path?.[0] || 'field';
            return `${field}: ${err.message}`;
          }).join(', ');
          errorMessage = `Validation errors: ${fieldErrors}`;
        } else if (errorData?.message) {
          errorMessage = errorData.message;
        }
      } catch (e) {
        // Use default error message
      }

      toast({
        title: "Failed to create page",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const updatePageMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) =>
      apiRequest("PUT", `/api/checkout-pages/${id}`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/checkout-pages"] });
      setIsCreating(false);
      resetForm();
      toast({
        title: "Checkout page updated!",
        description: "Your checkout page has been successfully updated.",
      });
    },
    onError: () => {
      toast({
        title: "Failed to update page",
        description: "Please check your information and try again.",
        variant: "destructive",
      });
    },
  });

  const deletePageMutation = useMutation({
    mutationFn: (id: number) => apiRequest("DELETE", `/api/checkout-pages/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/checkout-pages"] });
      toast({
        title: "Checkout page deleted!",
        description: "The checkout page has been permanently deleted.",
      });
    },
    onError: () => {
      toast({
        title: "Failed to delete page",
        description: "Please try again.",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setFormData({
      name: "",
      slug: "",
      title: "",
      description: "",
      productName: "PDF Tools Premium - Annual Subscription",
      price: "",
      currency: "USD",
      smtpConfigId: "",
      features: [
        "All premium features included",
        "Unlimited PDF processing",
        "Priority customer support",
        "30-day money-back guarantee"
      ],
      customerFields: {
        requireName: true,
        requireEmail: true,
        requirePhone: false,
        requireAddress: false,
        customFields: []
      },
      theme: {
        primaryColor: "#635BFF",
        secondaryColor: "#00D4FF",
        backgroundColor: "#FFFFFF",
        textColor: "#1A1F36",
        fontFamily: "Inter",
        layout: "centered",
        borderRadius: "8px",
        buttonStyle: "solid"
      },
      paymentGateways: {
        stripe: true,
        paypal: true,
        paypalStandard: false,
        paddle: false,
        sumup: false,
        wise: false,
        payoneer: false,
        bankTransfer: false,
        cod: false
      }
    });
    setSelectedPage(null);
  };

  const generateSlug = (name: string) => {
    return name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
  };

  const handleNameChange = (name: string) => {
    setFormData({
      ...formData,
      name,
      slug: generateSlug(name)
    });
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = "Page name is required";
    }

    if (!formData.title.trim()) {
      errors.title = "Page title is required";
    }

    if (!formData.productName.trim()) {
      errors.productName = "Product name is required";
    }

    if (!formData.price || isNaN(parseFloat(formData.price)) || parseFloat(formData.price) <= 0) {
      errors.price = "Price must be a valid number greater than 0";
    }

    if (!formData.smtpConfigId) {
      errors.smtpConfigId = "SMTP configuration is required";
    }

    // Check if at least one payment gateway is selected
    const hasPaymentGateway = Object.values(formData.paymentGateways).some(Boolean);
    if (!hasPaymentGateway) {
      errors.paymentGateways = "At least one payment gateway must be selected";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous validation errors
    setValidationErrors({});

    // Validate form
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the highlighted fields and try again.",
        variant: "destructive",
      });
      return;
    }

    // Generate slug from name if not provided
    const slug = formData.slug || formData.name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Prepare the data with proper types
    const submitData = {
      ...formData,
      slug,
      smtpConfigId: formData.smtpConfigId ? parseInt(formData.smtpConfigId) : null,
      price: formData.price.toString() // Keep as string for schema validation
    };

    console.log('Submitting checkout page data:', submitData);

    if (selectedPage) {
      // Update existing page
      updatePageMutation.mutate({ id: selectedPage.id, data: submitData });
    } else {
      // Create new page
      createPageMutation.mutate(submitData);
    }
  };

  const copyPageUrl = (slug: string) => {
    const url = `${window.location.origin}/checkout/${slug}`;
    navigator.clipboard.writeText(url);
    toast({
      title: "URL copied!",
      description: "Checkout page URL has been copied to clipboard.",
    });
  };

  const openPreview = (slug: string) => {
    window.open(`/checkout/${slug}`, '_blank');
  };

  const handleEditPage = (page: CheckoutPage) => {
    setSelectedPage(page);
    setFormData({
      name: page.name,
      slug: page.slug,
      title: page.title,
      description: page.description || "",
      productName: page.productName,
      price: page.price,
      currency: page.currency,
      smtpConfigId: page.smtpConfigId?.toString() || "",
      features: page.features || [
        "All premium features included",
        "Unlimited PDF processing",
        "Priority customer support",
        "30-day money-back guarantee"
      ],
      customerFields: page.customerFields,
      theme: page.theme,
      paymentGateways: page.paymentGateways
    });
    setIsCreating(true);
  };

  const handleDeletePage = (page: CheckoutPage) => {
    if (window.confirm(`Are you sure you want to delete "${page.name}"? This action cannot be undone.`)) {
      deletePageMutation.mutate(page.id);
    }
  };

  if (!user) {
    return <div>Please log in to access the checkout builder.</div>;
  }

  const isPremium = user?.isPremium || user?.role === 'admin' || user?.username === 'admin' || false;
  const isPrimaryAdmin = user?.isPrimaryAdmin || false;

  // Check if user is primary admin - this feature is restricted
  if (!isPrimaryAdmin) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-foreground mb-4">Checkout Page Builder</h1>
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-8">
              <ShoppingCart className="h-16 w-16 text-red-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-foreground mb-2">Access Restricted</h2>
              <p className="text-muted-foreground mb-6">
                The Checkout Builder is restricted to the primary admin user only. This feature is not available to other users, including secondary admins.
              </p>
              <Button variant="outline" asChild>
                <a href="/dashboard">Return to Dashboard</a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!isPremium) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-foreground mb-4">Checkout Page Builder</h1>
          <Card className="border-primary/20 bg-primary/5">
            <CardContent className="p-8">
              <ShoppingCart className="h-16 w-16 text-primary mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-foreground mb-2">Premium Feature</h2>
              <p className="text-muted-foreground mb-6">
                Create custom checkout pages with our visual builder. Upgrade to Premium to unlock this feature.
              </p>
              <Button className="bg-gradient-to-r from-primary to-secondary" asChild>
                <a href="/subscribe">Upgrade to Premium</a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Checkout Page Builder</h1>
          <p className="text-muted-foreground">
            Create beautiful, conversion-optimized checkout pages
          </p>
        </div>

        <Button onClick={() => {
          setIsCreating(true);
          setSelectedPage(null);
          resetForm();
        }} disabled={isCreating}>
          <Plus className="h-4 w-4 mr-2" />
          New Checkout Page
        </Button>
      </div>

      {isCreating ? (
        /* Create/Edit Form */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle>{selectedPage ? 'Edit Checkout Page' : 'Page Builder'}</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic">Basic</TabsTrigger>
                    <TabsTrigger value="fields">Fields</TabsTrigger>
                    <TabsTrigger value="design">Design</TabsTrigger>
                    <TabsTrigger value="payment">Payment</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4">
                    <div>
                      <Label htmlFor="page-name">Page Name *</Label>
                      <Input
                        id="page-name"
                        value={formData.name}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Premium Plan Checkout"
                        required
                        className={validationErrors.name ? "border-red-500" : ""}
                      />
                      {validationErrors.name && (
                        <p className="text-sm text-red-500 mt-1">{validationErrors.name}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="page-slug">URL Slug</Label>
                      <Input
                        id="page-slug"
                        value={formData.slug}
                        onChange={(e) => setFormData({...formData, slug: e.target.value})}
                        placeholder="premium-plan"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Will be available at: /checkout/{formData.slug}
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="page-title">Page Title *</Label>
                      <Input
                        id="page-title"
                        value={formData.title}
                        onChange={(e) => setFormData({...formData, title: e.target.value})}
                        placeholder="Upgrade to Premium"
                        required
                        className={validationErrors.title ? "border-red-500" : ""}
                      />
                      {validationErrors.title && (
                        <p className="text-sm text-red-500 mt-1">{validationErrors.title}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="page-description">Description</Label>
                      <Textarea
                        id="page-description"
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        placeholder="Unlock all features and process unlimited PDFs"
                      />
                    </div>

                    <div>
                      <Label htmlFor="product-name">Product Name *</Label>
                      <Input
                        id="product-name"
                        value={formData.productName}
                        onChange={(e) => setFormData({...formData, productName: e.target.value})}
                        placeholder="PDF Tools Premium - Annual Subscription"
                        required
                        className={validationErrors.productName ? "border-red-500" : ""}
                      />
                      {validationErrors.productName && (
                        <p className="text-sm text-red-500 mt-1">{validationErrors.productName}</p>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="price">Price *</Label>
                        <Input
                          id="price"
                          type="number"
                          step="0.01"
                          value={formData.price}
                          onChange={(e) => setFormData({...formData, price: e.target.value})}
                          placeholder="48.00"
                          required
                          className={validationErrors.price ? "border-red-500" : ""}
                        />
                        {validationErrors.price && (
                          <p className="text-sm text-red-500 mt-1">{validationErrors.price}</p>
                        )}
                      </div>
                      <div>
                        <Label htmlFor="currency">Currency</Label>
                        <Select
                          value={formData.currency}
                          onValueChange={(value) => setFormData({...formData, currency: value})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                            <SelectItem value="GBP">GBP</SelectItem>
                            <SelectItem value="CAD">CAD</SelectItem>
                            <SelectItem value="AUD">AUD</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label className="mb-3 block">Product Features</Label>
                      <p className="text-sm text-muted-foreground mb-4">
                        Add features that will be displayed on your checkout page to highlight the value of your product.
                      </p>
                      <div className="space-y-3">
                        {formData.features.map((feature, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Input
                              value={feature}
                              onChange={(e) => {
                                const newFeatures = [...formData.features];
                                newFeatures[index] = e.target.value;
                                setFormData({...formData, features: newFeatures});
                              }}
                              placeholder="Enter a feature..."
                              className="flex-1"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const newFeatures = formData.features.filter((_, i) => i !== index);
                                setFormData({...formData, features: newFeatures});
                              }}
                              className="px-3"
                            >
                              ✕
                            </Button>
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setFormData({
                              ...formData,
                              features: [...formData.features, "New feature"]
                            });
                          }}
                          className="w-full"
                        >
                          + Add Feature
                        </Button>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="smtp-config">Email Configuration *</Label>
                      <Select
                        value={formData.smtpConfigId}
                        onValueChange={(value) => setFormData({...formData, smtpConfigId: value})}
                      >
                        <SelectTrigger className={validationErrors.smtpConfigId ? "border-red-500" : ""}>
                          <SelectValue placeholder="Select SMTP Configuration" />
                        </SelectTrigger>
                        <SelectContent>
                          {smtpConfigs?.map((config: any) => (
                            <SelectItem key={config.id} value={config.id.toString()}>
                              {config.name} ({config.fromEmail})
                            </SelectItem>
                          ))}
                          {(!smtpConfigs || smtpConfigs.length === 0) && (
                            <SelectItem value="none" disabled>
                              No SMTP configurations found
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      {validationErrors.smtpConfigId && (
                        <p className="text-sm text-red-500 mt-1">{validationErrors.smtpConfigId}</p>
                      )}
                      <p className="text-sm text-muted-foreground mt-1">
                        SMTP servers are configured in Admin Settings. This will be used for sending confirmation emails.
                      </p>
                    </div>
                  </TabsContent>

                  <TabsContent value="fields" className="space-y-4">
                    <div>
                      <Label className="mb-3 block">Customer Information Fields</Label>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="font-medium">Customer Name</Label>
                            <p className="text-sm text-muted-foreground">Collect customer's full name</p>
                          </div>
                          <Switch
                            checked={formData.customerFields.requireName}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              customerFields: {...formData.customerFields, requireName: checked}
                            })}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="font-medium">Email Address</Label>
                            <p className="text-sm text-muted-foreground">Required for order confirmation</p>
                          </div>
                          <Switch
                            checked={formData.customerFields.requireEmail}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              customerFields: {...formData.customerFields, requireEmail: checked}
                            })}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="font-medium">Phone Number</Label>
                            <p className="text-sm text-muted-foreground">Optional contact information</p>
                          </div>
                          <Switch
                            checked={formData.customerFields.requirePhone}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              customerFields: {...formData.customerFields, requirePhone: checked}
                            })}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="font-medium">Billing Address</Label>
                            <p className="text-sm text-muted-foreground">Full billing address</p>
                          </div>
                          <Switch
                            checked={formData.customerFields.requireAddress}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              customerFields: {...formData.customerFields, requireAddress: checked}
                            })}
                          />
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="design" className="space-y-4">
                    <div>
                      <Label className="mb-3 block">Brand Colors</Label>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="primary-color">Primary Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="primary-color"
                              type="color"
                              value={formData.theme.primaryColor}
                              onChange={(e) => setFormData({
                                ...formData,
                                theme: {...formData.theme, primaryColor: e.target.value}
                              })}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={formData.theme.primaryColor}
                              onChange={(e) => setFormData({
                                ...formData,
                                theme: {...formData.theme, primaryColor: e.target.value}
                              })}
                              className="flex-1"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="secondary-color">Secondary Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="secondary-color"
                              type="color"
                              value={formData.theme.secondaryColor}
                              onChange={(e) => setFormData({
                                ...formData,
                                theme: {...formData.theme, secondaryColor: e.target.value}
                              })}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={formData.theme.secondaryColor}
                              onChange={(e) => setFormData({
                                ...formData,
                                theme: {...formData.theme, secondaryColor: e.target.value}
                              })}
                              className="flex-1"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="background-color">Background Color</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            id="background-color"
                            type="color"
                            value={formData.theme.backgroundColor}
                            onChange={(e) => setFormData({
                              ...formData,
                              theme: {...formData.theme, backgroundColor: e.target.value}
                            })}
                            className="w-12 h-10 p-1"
                          />
                          <Input
                            value={formData.theme.backgroundColor}
                            onChange={(e) => setFormData({
                              ...formData,
                              theme: {...formData.theme, backgroundColor: e.target.value}
                            })}
                            className="flex-1"
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="text-color">Text Color</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            id="text-color"
                            type="color"
                            value={formData.theme.textColor}
                            onChange={(e) => setFormData({
                              ...formData,
                              theme: {...formData.theme, textColor: e.target.value}
                            })}
                            className="w-12 h-10 p-1"
                          />
                          <Input
                            value={formData.theme.textColor}
                            onChange={(e) => setFormData({
                              ...formData,
                              theme: {...formData.theme, textColor: e.target.value}
                            })}
                            className="flex-1"
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="font-family">Typography</Label>
                      <Select
                        value={formData.theme.fontFamily}
                        onValueChange={(value) => setFormData({
                          ...formData,
                          theme: {...formData.theme, fontFamily: value}
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Inter">Inter</SelectItem>
                          <SelectItem value="SF Pro Display">SF Pro Display</SelectItem>
                          <SelectItem value="Roboto">Roboto</SelectItem>
                          <SelectItem value="Open Sans">Open Sans</SelectItem>
                          <SelectItem value="Poppins">Poppins</SelectItem>
                          <SelectItem value="Montserrat">Montserrat</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="layout">Layout Style</Label>
                        <Select
                          value={formData.theme.layout}
                          onValueChange={(value) => setFormData({
                            ...formData,
                            theme: {...formData.theme, layout: value}
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="centered">Centered</SelectItem>
                            <SelectItem value="split">Split Screen</SelectItem>
                            <SelectItem value="minimal">Minimal</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="border-radius">Border Radius</Label>
                        <Select
                          value={formData.theme.borderRadius}
                          onValueChange={(value) => setFormData({
                            ...formData,
                            theme: {...formData.theme, borderRadius: value}
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="0px">Sharp (0px)</SelectItem>
                            <SelectItem value="4px">Small (4px)</SelectItem>
                            <SelectItem value="8px">Medium (8px)</SelectItem>
                            <SelectItem value="12px">Large (12px)</SelectItem>
                            <SelectItem value="16px">Extra Large (16px)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="button-style">Button Style</Label>
                      <Select
                        value={formData.theme.buttonStyle}
                        onValueChange={(value) => setFormData({
                          ...formData,
                          theme: {...formData.theme, buttonStyle: value}
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="solid">Solid</SelectItem>
                          <SelectItem value="outline">Outline</SelectItem>
                          <SelectItem value="gradient">Gradient</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </TabsContent>

                  <TabsContent value="payment" className="space-y-4">
                    <div>
                      <Label className="mb-3 block">Payment Gateways</Label>
                      <p className="text-sm text-muted-foreground mb-4">
                        Select which payment methods to offer on your checkout page. All gateways are configured in Admin Settings.
                      </p>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                              <CreditCard className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <span className="font-medium">Stripe</span>
                              <p className="text-sm text-muted-foreground">Credit/debit cards, Apple Pay, Google Pay</p>
                            </div>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.stripe}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, stripe: checked}
                            })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center">
                              <CreditCard className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <span className="font-medium">PayPal</span>
                              <p className="text-sm text-muted-foreground">PayPal account payments</p>
                            </div>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.paypal}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, paypal: checked}
                            })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-700 rounded flex items-center justify-center">
                              <CreditCard className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <span className="font-medium">PayPal Standard</span>
                              <p className="text-sm text-muted-foreground">Simple PayPal checkout</p>
                            </div>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.paypalStandard}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, paypalStandard: checked}
                            })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-orange-500 rounded flex items-center justify-center">
                              <CreditCard className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <span className="font-medium">Paddle</span>
                              <p className="text-sm text-muted-foreground">Subscription billing platform</p>
                            </div>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.paddle}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, paddle: checked}
                            })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-teal-500 rounded flex items-center justify-center">
                              <CreditCard className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <span className="font-medium">SumUp</span>
                              <p className="text-sm text-muted-foreground">European payment processing</p>
                            </div>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.sumup}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, sumup: checked}
                            })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center">
                              <CreditCard className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <span className="font-medium">Wise</span>
                              <p className="text-sm text-muted-foreground">International money transfers</p>
                            </div>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.wise}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, wise: checked}
                            })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-purple-600 rounded flex items-center justify-center">
                              <CreditCard className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <span className="font-medium">Payoneer</span>
                              <p className="text-sm text-muted-foreground">Global payment platform</p>
                            </div>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.payoneer}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, payoneer: checked}
                            })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gray-600 rounded flex items-center justify-center">
                              <CreditCard className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <span className="font-medium">Bank Transfer</span>
                              <p className="text-sm text-muted-foreground">Direct bank account transfers</p>
                            </div>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.bankTransfer}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, bankTransfer: checked}
                            })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-orange-600 rounded flex items-center justify-center">
                              <Truck className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <span className="font-medium">Cash on Delivery</span>
                              <p className="text-sm text-muted-foreground">Payment collected upon delivery</p>
                            </div>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.cod}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, cod: checked}
                            })}
                          />
                        </div>
                      </div>

                      {validationErrors.paymentGateways && (
                        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <p className="text-sm text-red-600">{validationErrors.paymentGateways}</p>
                        </div>
                      )}

                      <div className="mt-4 p-3 bg-muted rounded-lg">
                        <p className="text-sm text-muted-foreground">
                          💡 <strong>Tip:</strong> Enable multiple payment methods to increase conversion rates.
                          Different customers prefer different payment options.
                        </p>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="flex gap-2">
                  <Button
                    type="submit"
                    disabled={createPageMutation.isPending || updatePageMutation.isPending}
                    className="flex-1"
                  >
                    {(createPageMutation.isPending || updatePageMutation.isPending) ? (
                      <>
                        <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                        {selectedPage ? 'Updating...' : 'Creating...'}
                      </>
                    ) : (
                      <>
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        {selectedPage ? 'Update Page' : 'Create Page'}
                      </>
                    )}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => {setIsCreating(false); resetForm();}}>
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Live Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Live Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg overflow-hidden">
                <div className="bg-muted p-3 border-b">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div className="flex-1 bg-background rounded px-3 py-1 text-xs text-muted-foreground ml-3">
                      checkout.pdftools.com/{formData.slug || 'your-page'}
                    </div>
                  </div>
                </div>

                <div
                  className="p-8"
                  style={{
                    backgroundColor: formData.theme.backgroundColor,
                    color: formData.theme.textColor,
                    fontFamily: formData.theme.fontFamily,
                  }}
                >
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold mb-2">
                      {formData.title || "Page Title"}
                    </h2>
                    <p className="text-muted-foreground">
                      {formData.description || "Page description"}
                    </p>
                  </div>

                  <div className="bg-muted rounded-lg p-6 mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <span className="font-medium">{formData.productName || "Product Name"}</span>
                      <span className="text-2xl font-bold">
                        {formData.currency} {formData.price || "0.00"}
                      </span>
                    </div>

                    {formData.features && formData.features.length > 0 && (
                      <div className="space-y-2 text-sm">
                        {formData.features.map((feature, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <div className="h-4 w-4 text-green-500">✓</div>
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Customer Fields Preview */}
                  <div className="space-y-3 mb-6">
                    {formData.customerFields.requireName && (
                      <div>
                        <label className="text-sm font-medium">Full Name *</label>
                        <div className="mt-1 h-10 bg-background border rounded px-3 flex items-center text-muted-foreground">
                          Enter your full name
                        </div>
                      </div>
                    )}
                    {formData.customerFields.requireEmail && (
                      <div>
                        <label className="text-sm font-medium">Email Address *</label>
                        <div className="mt-1 h-10 bg-background border rounded px-3 flex items-center text-muted-foreground">
                          <EMAIL>
                        </div>
                      </div>
                    )}
                    {formData.customerFields.requirePhone && (
                      <div>
                        <label className="text-sm font-medium">Phone Number</label>
                        <div className="mt-1 h-10 bg-background border rounded px-3 flex items-center text-muted-foreground">
                          +****************
                        </div>
                      </div>
                    )}
                    {formData.customerFields.requireAddress && (
                      <div>
                        <label className="text-sm font-medium">Billing Address</label>
                        <div className="mt-1 h-20 bg-background border rounded px-3 py-2 text-muted-foreground text-sm">
                          Street address, City, State, ZIP
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-3">
                    {formData.paymentGateways.stripe && (
                      <Button
                        className="w-full"
                        style={{
                          backgroundColor: formData.theme.primaryColor,
                          borderRadius: formData.theme.borderRadius
                        }}
                      >
                        💳 Pay with Stripe
                      </Button>
                    )}
                    {formData.paymentGateways.paypal && (
                      <Button
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        style={{ borderRadius: formData.theme.borderRadius }}
                      >
                        🅿️ Pay with PayPal
                      </Button>
                    )}
                    {formData.paymentGateways.paypalStandard && (
                      <Button
                        className="w-full bg-blue-700 hover:bg-blue-800"
                        style={{ borderRadius: formData.theme.borderRadius }}
                      >
                        💳 PayPal Standard
                      </Button>
                    )}
                    {formData.paymentGateways.paddle && (
                      <Button
                        className="w-full bg-orange-500 hover:bg-orange-600"
                        style={{ borderRadius: formData.theme.borderRadius }}
                      >
                        🏓 Pay with Paddle
                      </Button>
                    )}
                    {formData.paymentGateways.sumup && (
                      <Button
                        className="w-full bg-teal-500 hover:bg-teal-600"
                        style={{ borderRadius: formData.theme.borderRadius }}
                      >
                        💰 Pay with SumUp
                      </Button>
                    )}
                    {formData.paymentGateways.wise && (
                      <Button
                        className="w-full bg-green-600 hover:bg-green-700"
                        style={{ borderRadius: formData.theme.borderRadius }}
                      >
                        🌍 Pay with Wise
                      </Button>
                    )}
                    {formData.paymentGateways.payoneer && (
                      <Button
                        className="w-full bg-purple-600 hover:bg-purple-700"
                        style={{ borderRadius: formData.theme.borderRadius }}
                      >
                        💼 Pay with Payoneer
                      </Button>
                    )}
                    {formData.paymentGateways.bankTransfer && (
                      <Button
                        variant="outline"
                        className="w-full"
                        style={{ borderRadius: formData.theme.borderRadius }}
                      >
                        🏦 Bank Transfer
                      </Button>
                    )}
                    {formData.paymentGateways.cod && (
                      <Button
                        variant="outline"
                        className="w-full"
                        style={{ borderRadius: formData.theme.borderRadius }}
                      >
                        🚚 Cash on Delivery
                      </Button>
                    )}
                  </div>

                  <p className="text-xs text-center mt-6 text-muted-foreground">
                    Secure payment • 30-day money-back guarantee
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        /* Pages List */
        <div className="space-y-4">
          {!checkoutPages || checkoutPages.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <ShoppingCart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-foreground mb-2">No checkout pages yet</h3>
                <p className="text-muted-foreground mb-6">
                  Create your first checkout page to start accepting payments.
                </p>
                <Button onClick={() => setIsCreating(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Page
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {checkoutPages.map((page: CheckoutPage) => (
                <Card key={page.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{page.name}</CardTitle>
                      <Badge variant={page.isActive ? "default" : "secondary"}>
                        {page.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-muted-foreground">{page.description}</p>
                        <p className="text-2xl font-bold text-foreground mt-2">
                          {page.currency} {page.price}
                        </p>
                      </div>

                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <span>/checkout/{page.slug}</span>
                        <span>{new Date(page.createdAt).toLocaleDateString()}</span>
                      </div>

                      <div className="space-y-2">
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openPreview(page.slug)}
                            className="flex-1"
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            Preview
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyPageUrl(page.slug)}
                            className="flex-1"
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            Copy URL
                          </Button>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditPage(page)}
                            className="flex-1"
                          >
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeletePage(page)}
                            className="flex-1 text-destructive hover:text-destructive"
                            disabled={deletePageMutation.isPending}
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
