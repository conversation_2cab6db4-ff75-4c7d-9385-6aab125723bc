import { apiRequest } from "./queryClient";

export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  isPremium: boolean;
}

export const authApi = {
  login: async (username: string, password: string): Promise<{ user: User }> => {
    const response = await apiRequest("POST", "/api/auth/login", { username, password });
    return response.json();
  },

  register: async (username: string, email: string, password: string): Promise<{ user: User }> => {
    const response = await apiRequest("POST", "/api/auth/register", { username, email, password });
    return response.json();
  },

  logout: async (): Promise<void> => {
    await apiRequest("POST", "/api/auth/logout");
  },

  me: async (): Promise<{ user: User }> => {
    const response = await apiRequest("GET", "/api/auth/me");
    return response.json();
  },
};
