import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Check,
  Crown,
  Shield,
  Zap,
  Users,
  Building,
  Star,
  FileText,
  Image,
  Type,
  Lock,
  Download,
  Palette,
  Clock,
  Globe,
  Infinity,
  Mail,
  Phone,
  MessageCircle,
  ArrowRight,
  Sparkles
} from "lucide-react";

export default function Pricing() {
  const plans = [
    {
      name: "Free Starter",
      price: "$0",
      period: "forever",
      monthlyPrice: null,
      description: "Perfect for personal use and testing",
      badge: null,
      icon: FileText,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      features: [
        { name: "12 Essential PDF Zone Tools", included: true, highlight: true },
        { name: "Merge, Split, Rotate, Extract Pages", included: true },
        { name: "Basic Image Conversion (JPG, PNG)", included: true },
        { name: "Text File Conversion", included: true },
        { name: "10MB file size limit", included: true },
        { name: "20 operations per day", included: true },
        { name: "Community support", included: true },
        { name: "Advanced Document Conversion", included: false },
        { name: "PDF Security & Protection", included: false },
        { name: "Batch Processing", included: false },
        { name: "Priority Support", included: false }
      ],
      cta: "Start Free Today",
      popular: false
    },
    {
      name: "Professional",
      price: "$48",
      period: "per year",
      monthlyPrice: "$5",
      yearlyPrice: "$48",
      yearlyDiscount: "Save 20%",
      description: "Everything for professional workflows",
      badge: "Most Popular",
      icon: Crown,
      color: "text-primary",
      bgColor: "bg-primary/5",
      borderColor: "border-primary",
      features: [
        { name: "All 28 Premium PDF Zone Tools", included: true, highlight: true },
        { name: "Advanced Document Conversion (Word, Excel, PowerPoint)", included: true },
        { name: "All Image Formats (BMP, TIFF, etc.)", included: true },
        { name: "PDF Security & Protection", included: true },
        { name: "100MB file size limit", included: true },
        { name: "Unlimited operations", included: true, highlight: true },
        { name: "Batch processing & ZIP exports", included: true },
        { name: "Priority email support", included: true },
        { name: "Advanced analytics dashboard", included: true },
        { name: "API access", included: true },
        { name: "Custom integrations", included: false }
      ],
      cta: "Upgrade to Professional",
      popular: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "contact us",
      monthlyPrice: null,
      description: "For teams and large organizations",
      badge: "Contact Sales",
      icon: Building,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      features: [
        { name: "Everything in Professional", included: true, highlight: true },
        { name: "Unlimited file size", included: true },
        { name: "White-label solution", included: true },
        { name: "Custom integrations & API", included: true },
        { name: "Dedicated account manager", included: true },
        { name: "99.9% SLA guarantee", included: true },
        { name: "On-premise deployment option", included: true },
        { name: "Advanced security & compliance", included: true },
        { name: "Custom training & onboarding", included: true },
        { name: "Volume discounts", included: true },
        { name: "24/7 phone support", included: true }
      ],
      cta: "Contact Sales",
      popular: false
    }
  ];

  const faqs = [
    {
      question: "Can I change plans anytime?",
      answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately."
    },
    {
      question: "Is there a free trial for Premium?",
      answer: "Yes, all new users get a 14-day free trial of Premium features when they sign up."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards, PayPal, and bank transfers for annual plans."
    },
    {
      question: "Do you offer refunds?",
      answer: "Yes, we offer a 30-day money-back guarantee for all paid plans."
    },
    {
      question: "How does billing work?",
      answer: "Premium plans are billed annually. Enterprise plans can be billed monthly or annually."
    },
    {
      question: "Is my data secure?",
      answer: "Yes, all data is encrypted and processed in secure environments. Files are automatically deleted after processing."
    }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Navigation */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/">
                <span className="text-2xl font-bold text-primary">PDF Zone Pro</span>
              </Link>
              <div className="hidden md:block ml-10">
                <div className="flex items-baseline space-x-4">
                  <Link href="/#features">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Tools</span>
                  </Link>
                  <Link href="/pricing">
                    <span className="text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Pricing</span>
                  </Link>
                  <Link href="/about">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">About</span>
                  </Link>
                  <Link href="/contact">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Contact</span>
                  </Link>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-4 flex items-center md:ml-6 space-x-4">
                <Link href="/auth">
                  <Button variant="ghost">Login</Button>
                </Link>
                <Link href="/auth">
                  <Button>Sign Up Free</Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-indigo-50 py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center animate-fade-in">
            <div className="flex justify-center mb-6">
              <Badge className="bg-primary/10 text-primary border-primary/20 px-6 py-3 text-lg animate-pulse-slow">
                <Sparkles className="w-5 h-5 mr-2" />
                28 Professional PDF Zone Tools
              </Badge>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
              Simple, Transparent
              <span className="text-primary block animate-gradient bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                Pricing
              </span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Start free with essential tools, upgrade to unlock all 28 professional PDF features.
              No hidden fees, cancel anytime.
            </p>

            {/* Feature highlights */}
            <div className="flex flex-wrap justify-center gap-6 mb-8 text-sm text-muted-foreground stagger-animation">
              <div className="flex items-center hover-scale transition-transform duration-300">
                <Shield className="w-5 h-5 mr-2 text-green-500" />
                30-day money-back guarantee
              </div>
              <div className="flex items-center hover-scale transition-transform duration-300">
                <Zap className="w-5 h-5 mr-2 text-blue-500" />
                Instant activation
              </div>
              <div className="flex items-center hover-scale transition-transform duration-300">
                <Clock className="w-5 h-5 mr-2 text-purple-500" />
                Cancel anytime
              </div>
              <div className="flex items-center hover-scale transition-transform duration-300">
                <Globe className="w-5 h-5 mr-2 text-indigo-500" />
                Worldwide access
              </div>
            </div>

            {/* Pricing toggle */}
            <div className="flex items-center justify-center mb-8">
              <div className="bg-white rounded-full p-1 border-2 border-primary/20 shadow-lg">
                <div className="flex items-center">
                  <button className="px-6 py-2 rounded-full text-sm font-medium bg-primary text-white">
                    Yearly <span className="text-green-600 font-semibold ml-1">(Save 20%)</span>
                  </button>
                  <button className="px-6 py-2 rounded-full text-sm font-medium text-muted-foreground hover:text-foreground">
                    Monthly
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Plans */}
      <section className="py-24 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 stagger-animation">
            {plans.map((plan, index) => {
              const Icon = plan.icon;
              return (
                <Card
                  key={plan.name}
                  className={`relative card-hover hover-lift transition-all duration-300 border-2 ${plan.borderColor} ${plan.bgColor} ${
                    plan.popular ? 'shadow-xl scale-105 hover-glow' : 'hover:shadow-lg'
                  }`}
                >
                  {plan.badge && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 animate-bounce-slow">
                      <Badge className={`${
                        plan.popular
                          ? 'bg-gradient-to-r from-primary to-blue-600 text-white animate-gradient'
                          : 'bg-purple-100 text-purple-700 border border-purple-200'
                      } px-6 py-2 text-sm font-semibold`}>
                        {plan.popular && <Crown className="w-4 h-4 mr-2 animate-spin-slow" />}
                        {plan.badge}
                      </Badge>
                    </div>
                  )}

                  <CardHeader className="text-center pb-8">
                    <div className={`w-16 h-16 ${plan.bgColor} rounded-xl flex items-center justify-center mx-auto mb-4 hover-scale transition-transform duration-300 ${plan.popular ? 'animate-glow' : ''}`}>
                      <Icon className={`${plan.color} h-8 w-8 ${plan.popular ? 'animate-float' : 'icon-bounce'}`} />
                    </div>
                    <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                    <div className="mt-4">
                      <span className={`text-4xl font-bold text-foreground ${plan.popular ? 'animate-pulse-slow' : ''}`}>
                        {plan.price}
                      </span>
                      {plan.period && (
                        <span className="text-muted-foreground ml-2">/{plan.period}</span>
                      )}
                    </div>
                    {plan.monthlyPrice && plan.name === "Professional" && (
                      <div className="text-sm text-muted-foreground mt-2">
                        or {plan.monthlyPrice}/month <span className="text-green-600 font-semibold animate-pulse">({plan.yearlyDiscount})</span>
                      </div>
                    )}
                    <p className="text-muted-foreground mt-2">{plan.description}</p>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    <ul className="space-y-3">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className={`flex items-center space-x-3 animate-fade-in`} style={{ animationDelay: `${featureIndex * 50}ms` }}>
                          {feature.included ? (
                            <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                              <Check className="h-3 w-3 text-green-600" />
                            </div>
                          ) : (
                            <div className="w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                              <div className="h-2 w-2 bg-gray-400 rounded-full"></div>
                            </div>
                          )}
                          <span className={`text-sm ${
                            feature.included ? 'text-foreground' : 'text-muted-foreground line-through'
                          } ${feature.highlight ? 'font-semibold' : ''}`}>
                            {feature.name}
                          </span>
                        </li>
                      ))}
                    </ul>

                    <Link href={plan.name === 'Enterprise' ? '/contact' : '/auth'}>
                      <Button
                        className={`w-full text-lg py-6 btn-animate transition-all duration-300 ${
                          plan.popular
                            ? 'bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 hover-lift'
                            : plan.name === 'Enterprise'
                            ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white'
                            : 'border-2 hover:bg-primary/5 hover-scale'
                        }`}
                        variant={plan.popular ? 'default' : plan.name === 'Enterprise' ? 'default' : 'outline'}
                      >
                        <Icon className={`h-5 w-5 mr-2 ${plan.popular ? 'animate-bounce-slow' : 'icon-bounce'}`} />
                        {plan.cta}
                        {plan.name === 'Enterprise' && <ArrowRight className="h-4 w-4 ml-2" />}
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <div className="text-center mt-16">
            <div className="bg-white rounded-2xl p-8 border border-primary/20 shadow-lg animate-fade-in">
              <h3 className="text-2xl font-bold text-foreground mb-6">What's Included in Every Plan</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
                <div className="hover-scale transition-transform duration-300">
                  <Shield className="w-12 h-12 text-green-500 mx-auto mb-3" />
                  <h4 className="font-semibold text-foreground mb-2">Enterprise Security</h4>
                  <p className="text-sm text-muted-foreground">256-bit SSL encryption & secure processing</p>
                </div>
                <div className="hover-scale transition-transform duration-300">
                  <Zap className="w-12 h-12 text-blue-500 mx-auto mb-3" />
                  <h4 className="font-semibold text-foreground mb-2">Lightning Fast</h4>
                  <p className="text-sm text-muted-foreground">Process files in seconds, not minutes</p>
                </div>
                <div className="hover-scale transition-transform duration-300">
                  <Globe className="w-12 h-12 text-purple-500 mx-auto mb-3" />
                  <h4 className="font-semibold text-foreground mb-2">99.9% Uptime</h4>
                  <p className="text-sm text-muted-foreground">Reliable service you can count on</p>
                </div>
                <div className="hover-scale transition-transform duration-300">
                  <Star className="w-12 h-12 text-yellow-500 mx-auto mb-3" />
                  <h4 className="font-semibold text-foreground mb-2">4.9/5 Rating</h4>
                  <p className="text-sm text-muted-foreground">Loved by 50,000+ professionals</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* PDF Tools Comparison */}
      <section className="py-24 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Compare PDF Zone Tools by Plan</h2>
            <p className="text-xl text-muted-foreground">See exactly what tools you get with each plan</p>
          </div>

          <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-0">
              {/* Tool Categories */}
              <div className="bg-gray-50 p-6 border-r border-gray-200">
                <h3 className="font-semibold text-foreground mb-6">PDF Tool Categories</h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <FileText className="w-5 h-5 text-blue-500 mr-3" />
                    <span className="text-sm">Basic Manipulation (6 tools)</span>
                  </div>
                  <div className="flex items-center">
                    <Image className="w-5 h-5 text-purple-500 mr-3" />
                    <span className="text-sm">Image Conversion (8 tools)</span>
                  </div>
                  <div className="flex items-center">
                    <Type className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-sm">Document Conversion (8 tools)</span>
                  </div>
                  <div className="flex items-center">
                    <Palette className="w-5 h-5 text-orange-500 mr-3" />
                    <span className="text-sm">Enhancement (4 tools)</span>
                  </div>
                  <div className="flex items-center">
                    <Lock className="w-5 h-5 text-red-500 mr-3" />
                    <span className="text-sm">Security (2 tools)</span>
                  </div>
                </div>
              </div>

              {/* Free Plan */}
              <div className="p-6 border-r border-gray-200">
                <div className="text-center mb-6">
                  <FileText className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <h4 className="font-semibold text-foreground">Free Starter</h4>
                  <p className="text-sm text-muted-foreground">12 Essential Tools</p>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span>Basic Tools</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Basic Images</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Text Conversion</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Advanced Features</span>
                    <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Security Tools</span>
                    <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
                  </div>
                </div>
              </div>

              {/* Professional Plan */}
              <div className="p-6 border-r border-gray-200 bg-primary/5">
                <div className="text-center mb-6">
                  <Crown className="w-8 h-8 text-primary mx-auto mb-2" />
                  <h4 className="font-semibold text-foreground">Professional</h4>
                  <p className="text-sm text-muted-foreground">All 28 Tools</p>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span>All Basic Tools</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>All Image Formats</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>All Documents</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Enhancement Tools</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Security & Protection</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                </div>
              </div>

              {/* Enterprise Plan */}
              <div className="p-6">
                <div className="text-center mb-6">
                  <Building className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <h4 className="font-semibold text-foreground">Enterprise</h4>
                  <p className="text-sm text-muted-foreground">Everything + More</p>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span>All Professional</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Custom Integration</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>White-label</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Unlimited Size</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>24/7 Support</span>
                    <Check className="w-4 h-4 text-green-500" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-muted/30">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-muted-foreground">
              Everything you need to know about our pricing
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <h3 className="font-semibold text-foreground mb-3">{faq.question}</h3>
                  <p className="text-muted-foreground">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Enterprise Contact Section */}
      <section className="py-24 bg-gradient-to-br from-purple-50 to-indigo-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Need Enterprise Solutions?</h2>
            <p className="text-xl text-muted-foreground">Get custom pricing and dedicated support for your organization</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <Card className="text-center p-8 hover-lift transition-all duration-300">
              <Phone className="w-12 h-12 text-blue-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">Call Sales</h3>
              <p className="text-muted-foreground mb-4">Speak directly with our sales team</p>
              <Button variant="outline" className="w-full">
                <Phone className="w-4 h-4 mr-2" />
                Schedule Call
              </Button>
            </Card>

            <Card className="text-center p-8 hover-lift transition-all duration-300">
              <Mail className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">Email Us</h3>
              <p className="text-muted-foreground mb-4">Get a detailed proposal via email</p>
              <Button variant="outline" className="w-full">
                <Mail className="w-4 h-4 mr-2" />
                Send Email
              </Button>
            </Card>

            <Card className="text-center p-8 hover-lift transition-all duration-300">
              <MessageCircle className="w-12 h-12 text-purple-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">Live Chat</h3>
              <p className="text-muted-foreground mb-4">Chat with our experts right now</p>
              <Button variant="outline" className="w-full">
                <MessageCircle className="w-4 h-4 mr-2" />
                Start Chat
              </Button>
            </Card>
          </div>

          <div className="text-center mt-12">
            <div className="bg-white rounded-2xl p-8 border border-purple-200 shadow-lg">
              <h3 className="text-2xl font-bold text-foreground mb-4">Enterprise Benefits</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                  <Building className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <h4 className="font-semibold text-foreground">Custom Deployment</h4>
                  <p className="text-sm text-muted-foreground">On-premise or private cloud options</p>
                </div>
                <div>
                  <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <h4 className="font-semibold text-foreground">Dedicated Support</h4>
                  <p className="text-sm text-muted-foreground">24/7 priority support with SLA</p>
                </div>
                <div>
                  <Shield className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <h4 className="font-semibold text-foreground">Advanced Security</h4>
                  <p className="text-sm text-muted-foreground">SOC 2 compliance & custom security</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-primary to-blue-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 animate-fade-in">
            Ready to Transform Your PDF Workflow?
          </h2>
          <p className="text-xl text-white/90 mb-8 animate-slide-up">
            Join 50,000+ professionals who trust PDF Zone Pro for their document processing needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-scale-in">
            <Link href="/auth">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-6 btn-animate hover-lift">
                <Crown className="w-5 h-5 mr-2" />
                Start Free Today
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-primary btn-animate">
                <Building className="w-5 h-5 mr-2" />
                Contact Sales
              </Button>
            </Link>
          </div>
          <p className="text-white/80 text-sm mt-6">
            <Shield className="w-4 h-4 inline mr-1" />
            No credit card required • 30-day money-back guarantee • Cancel anytime
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-foreground text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-1">
              <div className="flex items-center mb-4">
                <span className="text-2xl font-bold">PDF Zone Pro</span>
              </div>
              <p className="text-gray-300 mb-4">
                Professional PDF toolkit with 28 comprehensive tools for document processing, conversion, and security.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/#features"><span className="hover:text-white cursor-pointer">PDF Zone</span></Link></li>
                <li><Link href="/#features"><span className="hover:text-white cursor-pointer">Document Conversion</span></Link></li>
                <li><Link href="/#features"><span className="hover:text-white cursor-pointer">Image Processing</span></Link></li>
                <li><Link href="/pricing"><span className="hover:text-white cursor-pointer">Pricing</span></Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/about"><span className="hover:text-white cursor-pointer">About Us</span></Link></li>
                <li><Link href="/contact"><span className="hover:text-white cursor-pointer">Contact</span></Link></li>
                <li><Link href="/privacy"><span className="hover:text-white cursor-pointer">Privacy Policy</span></Link></li>
                <li><Link href="/terms"><span className="hover:text-white cursor-pointer">Terms of Service</span></Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/contact"><span className="hover:text-white cursor-pointer">Help Center</span></Link></li>
                <li><Link href="/contact"><span className="hover:text-white cursor-pointer">Contact Support</span></Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-300 text-sm">
              © 2024 PDF Zone Pro. All rights reserved.
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <span className="text-gray-300 text-sm">Secured by</span>
              <div className="flex items-center space-x-2">
                <Shield className="text-green-500 h-4 w-4" />
                <span className="text-sm">256-bit SSL</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
