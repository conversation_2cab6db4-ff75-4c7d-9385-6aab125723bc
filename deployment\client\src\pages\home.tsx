import { <PERSON> } from "wouter";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import DynamicFooter from "@/components/DynamicFooter";
import {
  FileText,
  Shield,
  FileImage,
  RotateCw,
  Scissors,
  Palette,
  Image,
  Type,
  Download,
  Lock,
  Check,
  Crown,
  Star,
  Users,
  DollarSign,
  TrendingUp,
  Zap,
  Infinity,
  Clock,
  Globe
} from "lucide-react";

export default function Home() {
  const plans = [
    {
      name: "Free Starter",
      price: "$0",
      period: "forever",
      monthlyPrice: null,
      description: "Perfect for personal use and testing",
      badge: null,
      icon: FileText,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      features: [
        { name: "12 Essential PDF Zone Tools", included: true, highlight: true },
        { name: "Merge, Split, Rotate, Extract Pages", included: true },
        { name: "Basic Image Conversion (JPG, PNG)", included: true },
        { name: "Text File Conversion", included: true },
        { name: "10MB file size limit", included: true },
        { name: "20 operations per day", included: true },
        { name: "Community support", included: true },
        { name: "Advanced Document Conversion", included: false },
        { name: "PDF Security & Protection", included: false },
        { name: "Batch Processing", included: false },
        { name: "Priority Support", included: false }
      ],
      cta: "Start Free Today",
      popular: false
    },
    {
      name: "Professional",
      price: "$4",
      period: "per month",
      monthlyPrice: "$4",
      yearlyPrice: "$39",
      yearlyDiscount: "Save 19%",
      description: "Everything for professional workflows",
      badge: "Most Popular",
      icon: Crown,
      color: "text-primary",
      bgColor: "bg-primary/5",
      borderColor: "border-primary",
      features: [
        { name: "All 28 Premium PDF Zone Tools", included: true, highlight: true },
        { name: "Advanced Document Conversion (Word, Excel, PowerPoint)", included: true },
        { name: "All Image Formats (BMP, TIFF, etc.)", included: true },
        { name: "PDF Security & Protection", included: true },
        { name: "100MB file size limit", included: true },
        { name: "Unlimited operations", included: true, highlight: true },
        { name: "Batch processing & ZIP exports", included: true },
        { name: "Priority email support", included: true },
        { name: "Advanced analytics dashboard", included: true },
        { name: "API access", included: true },
        { name: "Custom integrations", included: false }
      ],
      cta: "Upgrade to Professional",
      popular: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "contact us",
      monthlyPrice: null,
      description: "For teams and large organizations",
      badge: "Contact Sales",
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      features: [
        { name: "Everything in Professional", included: true, highlight: true },
        { name: "Unlimited file size", included: true },
        { name: "White-label solution", included: true },
        { name: "Custom integrations & API", included: true },
        { name: "Dedicated account manager", included: true },
        { name: "99.9% SLA guarantee", included: true },
        { name: "On-premise deployment option", included: true },
        { name: "Advanced security & compliance", included: true },
        { name: "Custom training & onboarding", included: true },
        { name: "Volume discounts", included: true },
        { name: "24/7 phone support", included: true }
      ],
      cta: "Contact Sales",
      popular: false
    }
  ];

  const features = [
    {
      icon: FileText,
      title: "PDF Manipulation",
      description: "Complete PDF editing toolkit with merge, split, rotate, organize, and page extraction capabilities.",
      items: ["Merge & Split PDFs", "Rotate & Organize Pages", "Extract Specific Pages", "Remove Unwanted Pages"],
      color: "text-blue-500",
      bgColor: "bg-blue-50"
    },
    {
      icon: Image,
      title: "Image Conversion",
      description: "Convert between PDF and image formats including JPG, PNG, BMP, and TIFF with high quality.",
      items: ["PDF to Images", "Images to PDF", "Batch Processing", "Multiple Formats"],
      color: "text-purple-500",
      bgColor: "bg-purple-50"
    },
    {
      icon: Type,
      title: "Document Conversion",
      description: "Transform documents between PDF, Word, PowerPoint, Excel, and text formats seamlessly.",
      items: ["Word ↔ PDF", "PowerPoint ↔ PDF", "Excel ↔ PDF", "Text ↔ PDF"],
      color: "text-green-500",
      bgColor: "bg-green-50"
    },
    {
      icon: Palette,
      title: "PDF Enhancement",
      description: "Enhance your PDFs with compression, grayscale conversion, watermarks, and repair tools.",
      items: ["Smart Compression", "Grayscale Conversion", "Add Watermarks", "Repair Corrupted PDFs"],
      color: "text-orange-500",
      bgColor: "bg-orange-50"
    },
    {
      icon: Lock,
      title: "Security & Protection",
      description: "Secure your documents with password protection, encryption, and permission controls.",
      items: ["Password Protection", "Remove Passwords", "Access Permissions", "Digital Security"],
      color: "text-red-500",
      bgColor: "bg-red-50"
    },
    {
      icon: Download,
      title: "Export & Archive",
      description: "Export PDFs to various formats and create ZIP archives with organized file structures.",
      items: ["Multi-format Export", "ZIP Archives", "Organized Downloads", "Batch Export"],
      color: "text-indigo-500",
      bgColor: "bg-indigo-50"
    }
  ];

  const stats = [
    { label: "Total Users", value: "2,847", change: "+12%", icon: Users },
    { label: "Revenue", value: "$14,250", change: "+8%", icon: DollarSign },
    { label: "PDF Processed", value: "45,892", change: "+24%", icon: FileText }
  ];

  const activities = [
    { description: "User merged 5 PDF files", time: "2 minutes ago", icon: FileText, color: "text-primary" },
    { description: "New premium subscription", time: "5 minutes ago", icon: Crown, color: "text-green-500" },
    { description: "PDF converted to Word", time: "12 minutes ago", icon: Type, color: "text-secondary" }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Navigation */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <span className="text-2xl font-bold text-primary">PDF Zone Pro</span>
              <div className="hidden md:block ml-10">
                <div className="flex items-baseline space-x-4">
                  <a href="#features" className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Tools</a>
                  <Link href="/pricing">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Pricing</span>
                  </Link>
                  <Link href="/about">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">About</span>
                  </Link>
                  <Link href="/contact">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Contact</span>
                  </Link>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-4 flex items-center md:ml-6 space-x-4">
                <Link href="/auth">
                  <Button variant="ghost">Login</Button>
                </Link>
                <Link href="/auth">
                  <Button>Sign Up Free</Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-purple-50 to-indigo-50 py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center animate-fade-in">
            <div className="flex justify-center mb-6">
              <Badge className="bg-primary/10 text-primary border-primary/20 px-4 py-2">
                <Crown className="w-4 h-4 mr-2" />
                28 Professional PDF Zone Tools
              </Badge>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
              Complete PDF Toolkit
              <span className="text-primary block">for Professionals</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Transform, secure, and optimize your PDFs with 28 powerful tools. Convert between formats,
              enhance documents, and manage your workflow with enterprise-grade features.
            </p>

            {/* Feature highlights */}
            <div className="flex flex-wrap justify-center gap-4 mb-8 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Zap className="w-4 h-4 mr-2 text-primary" />
                Lightning Fast Processing
              </div>
              <div className="flex items-center">
                <Shield className="w-4 h-4 mr-2 text-green-500" />
                Enterprise Security
              </div>
              <div className="flex items-center">
                <Globe className="w-4 h-4 mr-2 text-blue-500" />
                Cloud-Based Platform
              </div>
              <div className="flex items-center">
                <Infinity className="w-4 h-4 mr-2 text-purple-500" />
                Unlimited Processing
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/auth">
                <Button size="lg" className="text-lg px-8 py-6 bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 btn-animate hover-lift animate-pulse-slow">
                  <Crown className="w-5 h-5 mr-2 animate-bounce-slow" />
                  Start Free Today
                </Button>
              </Link>
              <Link href="/tools">
                <Button variant="outline" size="lg" className="text-lg px-8 py-6 border-primary text-primary hover:bg-primary/5 btn-animate hover-scale">
                  <FileText className="w-5 h-5 mr-2 icon-bounce" />
                  Explore All Tools
                </Button>
              </Link>
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              <Clock className="w-4 h-4 inline mr-1" />
              No credit card required • 30-day money-back guarantee
            </p>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section id="features" className="py-24 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">28 Professional PDF Zone Tools in One Platform</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              From basic manipulation to advanced conversions and security features. Everything you need to work with PDFs professionally.
            </p>
            <div className="flex justify-center mt-6">
              <Badge variant="outline" className="text-primary border-primary/30">
                <Star className="w-4 h-4 mr-2 fill-current" />
                Most Comprehensive PDF Toolkit Available
              </Badge>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 stagger-animation">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card key={feature.title} className="card-hover hover-lift border-2 hover:border-primary/20 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className={`w-16 h-16 ${feature.bgColor} rounded-xl flex items-center justify-center mb-6 hover-scale transition-transform duration-300`}>
                      <Icon className={`${feature.color} h-8 w-8 icon-bounce`} />
                    </div>
                    <h3 className="text-xl font-semibold text-foreground mb-4">{feature.title}</h3>
                    <p className="text-muted-foreground mb-6">{feature.description}</p>
                    <ul className="text-sm text-muted-foreground space-y-2">
                      {feature.items.map((item, itemIndex) => (
                        <li key={item} className="flex items-center animate-fade-in" style={{ animationDelay: `${(index * 100) + (itemIndex * 50)}ms` }}>
                          <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <Check className="h-3 w-3 text-green-600" />
                          </div>
                          {item}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-24 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Simple, Transparent Pricing</h2>
            <p className="text-xl text-muted-foreground">Start free, upgrade when you need more power. No hidden fees.</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 stagger-animation">
            {plans.map((plan, index) => {
              const Icon = plan.icon;
              return (
                <Card
                  key={plan.name}
                  className={`relative card-hover hover-lift transition-all duration-300 border-2 ${plan.borderColor} ${plan.bgColor} ${
                    plan.popular ? 'shadow-xl scale-105 hover-glow' : 'hover:shadow-lg'
                  }`}
                >
                  {plan.badge && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 animate-bounce-slow">
                      <Badge className={`${
                        plan.popular
                          ? 'bg-gradient-to-r from-primary to-blue-600 text-white animate-gradient'
                          : 'bg-purple-100 text-purple-700 border border-purple-200'
                      } px-6 py-2 text-sm font-semibold`}>
                        {plan.popular && <Crown className="w-4 h-4 mr-2 animate-spin-slow" />}
                        {plan.badge}
                      </Badge>
                    </div>
                  )}

                  <CardContent className="p-8">
                    <div className="text-center mb-8">
                      <div className={`w-16 h-16 ${plan.bgColor} rounded-xl flex items-center justify-center mx-auto mb-4 hover-scale transition-transform duration-300 ${plan.popular ? 'animate-glow' : ''}`}>
                        <Icon className={`${plan.color} h-8 w-8 ${plan.popular ? 'animate-float' : 'icon-bounce'}`} />
                      </div>
                      <h3 className="text-2xl font-bold text-foreground mb-2">{plan.name}</h3>
                      <div className="mt-4">
                        <span className={`text-4xl font-bold text-foreground ${plan.popular ? 'animate-pulse-slow' : ''}`}>
                          {plan.price}
                        </span>
                        {plan.period && (
                          <span className="text-muted-foreground ml-2">/{plan.period}</span>
                        )}
                      </div>
                      {plan.yearlyPrice && (
                        <div className="text-sm text-muted-foreground mt-2">
                          or {plan.yearlyPrice}/year <span className="text-green-600 font-semibold animate-pulse">({plan.yearlyDiscount})</span>
                        </div>
                      )}
                      <p className="text-muted-foreground mt-2">{plan.description}</p>
                    </div>

                    <ul className="space-y-3 mb-8">
                      {plan.features.slice(0, 7).map((feature, featureIndex) => (
                        <li key={featureIndex} className={`flex items-center space-x-3 animate-fade-in`} style={{ animationDelay: `${featureIndex * 50}ms` }}>
                          {feature.included ? (
                            <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                              <Check className="h-3 w-3 text-green-600" />
                            </div>
                          ) : (
                            <div className="w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                              <div className="h-2 w-2 bg-gray-400 rounded-full"></div>
                            </div>
                          )}
                          <span className={`text-sm ${
                            feature.included ? 'text-foreground' : 'text-muted-foreground line-through'
                          } ${feature.highlight ? 'font-semibold' : ''}`}>
                            {feature.name}
                          </span>
                        </li>
                      ))}
                    </ul>

                    <Link href={plan.name === 'Enterprise' ? '/contact' : '/auth'}>
                      <Button
                        className={`w-full text-lg py-6 btn-animate transition-all duration-300 ${
                          plan.popular
                            ? 'bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 hover-lift'
                            : plan.name === 'Enterprise'
                            ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white'
                            : 'border-2 hover:bg-primary/5 hover-scale'
                        }`}
                        variant={plan.popular ? 'default' : plan.name === 'Enterprise' ? 'default' : 'outline'}
                      >
                        <Icon className={`h-5 w-5 mr-2 ${plan.popular ? 'animate-bounce-slow' : 'icon-bounce'}`} />
                        {plan.cta}
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <div className="text-center mt-12">
            <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground mb-4">
              <div className="flex items-center">
                <Shield className="w-4 h-4 mr-2 text-green-500" />
                30-day money-back guarantee
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2 text-blue-500" />
                Cancel anytime
              </div>
              <div className="flex items-center">
                <Star className="w-4 h-4 mr-2 text-yellow-500" />
                No setup fees
              </div>
            </div>
            <p className="text-muted-foreground">
              Join thousands of professionals who trust our PDF toolkit for their daily workflows
            </p>
          </div>
        </div>
      </section>

      {/* PDF Zone Tools Showcase */}
      <section className="py-24 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">See Our PDF Zone Tools in Action</h2>
            <p className="text-xl text-muted-foreground">Professional-grade PDF processing with an intuitive interface</p>
          </div>

          <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-8 border border-gray-200 shadow-lg">
            {/* Tools Header */}
            <div className="text-center mb-8">
              <Badge className="bg-primary/10 text-primary border-primary/20 mb-4">
                <Zap className="w-4 h-4 mr-2" />
                Live Demo Interface
              </Badge>
              <h3 className="text-2xl font-bold text-foreground mb-2">28 Professional PDF Zone Tools</h3>
              <p className="text-muted-foreground">Click any tool to see it in action</p>
            </div>

            {/* PDF Zone Tools Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 stagger-animation">
              {[
                { icon: FileText, name: "Merge PDF", color: "text-blue-500", bg: "bg-blue-50" },
                { icon: Scissors, name: "Split PDF", color: "text-green-500", bg: "bg-green-50" },
                { icon: RotateCw, name: "Rotate PDF", color: "text-purple-500", bg: "bg-purple-50" },
                { icon: Image, name: "JPG to PDF", color: "text-pink-500", bg: "bg-pink-50" },
                { icon: Type, name: "Word to PDF", color: "text-indigo-500", bg: "bg-indigo-50" },
                { icon: Lock, name: "Protect PDF", color: "text-red-500", bg: "bg-red-50" },
                { icon: Palette, name: "Grayscale", color: "text-gray-500", bg: "bg-gray-50" },
                { icon: Download, name: "PDF to ZIP", color: "text-orange-500", bg: "bg-orange-50" },
                { icon: FileImage, name: "PNG to PDF", color: "text-cyan-500", bg: "bg-cyan-50" },
                { icon: Shield, name: "Unlock PDF", color: "text-emerald-500", bg: "bg-emerald-50" },
                { icon: FileText, name: "Extract Pages", color: "text-yellow-600", bg: "bg-yellow-50" },
                { icon: TrendingUp, name: "Compress", color: "text-violet-500", bg: "bg-violet-50" }
              ].map((tool, index) => {
                const Icon = tool.icon;
                return (
                  <Card key={tool.name} className={`hover:shadow-lg transition-all duration-300 cursor-pointer ${tool.bg} border-2 hover:border-primary/30 card-hover hover-lift`}>
                    <CardContent className="p-4 text-center">
                      <div className={`w-12 h-12 ${tool.bg} rounded-lg flex items-center justify-center mx-auto mb-3 border hover-scale transition-transform duration-300`}>
                        <Icon className={`${tool.color} h-6 w-6 icon-bounce`} />
                      </div>
                      <h4 className="text-sm font-semibold text-foreground">{tool.name}</h4>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* CTA Section */}
            <div className="text-center mt-8 p-6 bg-primary/5 rounded-xl border border-primary/20">
              <h4 className="text-lg font-semibold text-foreground mb-2">Ready to try all 28 tools?</h4>
              <p className="text-muted-foreground mb-4">Start with our free tier and upgrade when you need more power</p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link href="/tools">
                  <Button className="bg-primary hover:bg-primary/90">
                    <FileText className="w-4 h-4 mr-2" />
                    Try Tools Now
                  </Button>
                </Link>
                <Link href="/auth">
                  <Button variant="outline" className="border-primary text-primary hover:bg-primary/5">
                    <Crown className="w-4 h-4 mr-2" />
                    Sign Up Free
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Trusted by Professionals Worldwide</h2>
            <p className="text-xl text-muted-foreground">See what our users say about their PDF workflow transformation</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-8">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-6 italic">
                  "PDF Zone Pro has revolutionized our document workflow. The 28 tools cover everything we need,
                  and the batch processing saves us hours every week."
                </p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                    SM
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground">Sarah Mitchell</h4>
                    <p className="text-sm text-muted-foreground">Marketing Director, TechCorp</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Testimonial 2 */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-8">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-6 italic">
                  "The document conversion features are incredible. Converting between PDF, Word, and Excel
                  formats is seamless and maintains perfect formatting."
                </p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                    JD
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground">James Davis</h4>
                    <p className="text-sm text-muted-foreground">Legal Consultant</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Testimonial 3 */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-8">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-6 italic">
                  "As a freelance designer, I need reliable PDF Zone tools. The security features and image
                  conversion capabilities are exactly what I was looking for."
                </p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                    AL
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground">Anna Lopez</h4>
                    <p className="text-sm text-muted-foreground">Freelance Designer</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Stats Section */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-primary mb-2">50K+</div>
              <p className="text-muted-foreground">Active Users</p>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">2M+</div>
              <p className="text-muted-foreground">PDFs Processed</p>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">99.9%</div>
              <p className="text-muted-foreground">Uptime</p>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">4.9/5</div>
              <p className="text-muted-foreground">User Rating</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-primary">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Transform Your PDF Workflow?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Join thousands of professionals who trust PDF Zone Pro for their document processing needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-6">
                Get Started Free
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-primary">
              Schedule Demo
            </Button>
          </div>
          <p className="text-white/80 text-sm mt-6">No credit card required • Upgrade anytime</p>
        </div>
      </section>

      {/* Footer */}
      <DynamicFooter />
    </div>
  );
}
